from app.fiass_core import FIA<PERSON>
from app.memory_summarizer import MemorySummarizer
from app.postgresql_memory import <PERSON>gresMemory
from app.database import db
from app.config import config
from app.mt5_manager import mt5_manager
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import json
import re
import openai
import os
from typing import Dict, Any, List

class ChatAgent:
    def __init__(self):
        self.fiass = FIASS()
        self.memory = PostgresMemory()
        self.summarizer = MemorySummarizer()

        # Initialize OpenAI
        self.openai_client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY', config.OPENAI_API_KEY)
        )

        # System context for the AI
        self.system_context = self._build_system_context()

    def _build_system_context(self) -> str:
        """Build comprehensive system context for the AI"""
        try:
            # Get current system state
            account_info = self._get_current_account_state()
            bot_status = self._get_current_bot_state()

            return f"""You are NexGen TraderPro AI Assistant, an advanced conversational AI specialized in forex trading and financial analysis. You can handle any conversation topic while being exceptionally knowledgeable about trading.

CURRENT SYSTEM STATE:
{account_info}
{bot_status}

CAPABILITIES:
1. **General Conversation**: You can chat about anything - weather, technology, philosophy, etc. Be natural and engaging.
2. **Trading Expertise**: Provide expert analysis on forex, technical indicators, market conditions, and trading strategies.
3. **System Control**: You can read and modify system configurations, check account states, and control trading operations.
4. **Real-time Data**: Access live market data, account information, and trading history.

PERSONALITY:
- Professional yet friendly and conversational
- Knowledgeable about trading but accessible to beginners
- Helpful and proactive in offering insights
- Can engage in casual conversation while maintaining expertise

TRADING FUNCTIONS AVAILABLE:
- get_bot_status(): Check system health
- get_account_info(): Live account data
- get_open_trades(): Current positions
- get_recent_trades(): Trade history
- get_performance_summary(): P&L analysis
- get_market_analysis(): Latest signals
- get_risk_assessment(): Risk metrics
- run_reasoning_cycle(): Generate new signals
- modify_config(param, value): Change settings
- execute_trade(symbol, action, volume): Place trades

Always be helpful, engaging, and provide value whether the user wants to chat casually or discuss complex trading strategies."""
        except Exception as e:
            return "You are NexGen TraderPro AI Assistant, a conversational AI specialized in forex trading. You can chat about anything while providing expert trading insights."

    def process_message(self, message: str) -> str:
        """Process any message using GPT with trading context"""
        try:
            # Check if this is a trading-specific command that needs immediate data
            trading_data = self._extract_trading_data_if_needed(message)

            # Prepare messages for OpenAI
            messages = [
                {"role": "system", "content": self.system_context},
                {"role": "user", "content": message}
            ]

            # Add trading data context if relevant
            if trading_data:
                messages.insert(-1, {"role": "system", "content": f"CURRENT DATA:\n{trading_data}"})

            # Get response from OpenAI
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )

            ai_response = response.choices[0].message.content

            # Process any function calls mentioned in the response
            processed_response = self._process_function_calls(ai_response, message)

            return processed_response

        except Exception as e:
            # Fallback to pattern-based responses if OpenAI fails
            return self._fallback_response(message, str(e))

    def _extract_trading_data_if_needed(self, message: str) -> str:
        """Extract relevant trading data based on the message content"""
        msg = message.lower()
        data_parts = []

        try:
            if any(word in msg for word in ['account', 'balance', 'equity', 'margin', 'money']):
                data_parts.append(self._get_account_info())

            if any(word in msg for word in ['trade', 'position', 'open', 'current']):
                data_parts.append(self._get_open_trades())

            if any(word in msg for word in ['performance', 'profit', 'loss', 'pnl']):
                data_parts.append(self._get_performance_summary())

            if any(word in msg for word in ['status', 'health', 'running', 'bot']):
                data_parts.append(self._get_bot_status())

            if any(word in msg for word in ['risk', 'exposure', 'assessment']):
                data_parts.append(self._get_risk_assessment())

            return "\n\n".join(data_parts) if data_parts else ""

        except Exception as e:
            return f"Error retrieving trading data: {str(e)}"

    def _process_function_calls(self, ai_response: str, original_message: str) -> str:
        """Process any function calls mentioned in the AI response"""
        try:
            # Check if AI wants to execute specific functions
            if "run_reasoning_cycle()" in ai_response:
                try:
                    self.fiass.run_all()
                    ai_response = ai_response.replace("run_reasoning_cycle()", "✅ Reasoning cycle completed successfully")
                except Exception as e:
                    ai_response = ai_response.replace("run_reasoning_cycle()", f"❌ Error running reasoning cycle: {str(e)}")

            # Add more function processing as needed
            return ai_response

        except Exception as e:
            return ai_response

    def _fallback_response(self, message: str, error: str) -> str:
        """Fallback response system when OpenAI is unavailable"""
        msg = message.lower().strip()

        # Handle greetings
        if any(word in msg for word in ['hi', 'hello', 'hey', 'good morning', 'good afternoon']):
            return "👋 Hello! I'm your NexGen TraderPro AI Assistant. I can help with trading analysis, account management, or just have a friendly chat. What would you like to discuss?"

        # Handle trading-specific queries with existing methods
        if self._matches_pattern(msg, ['status', 'bot', 'health', 'running']):
            return self._get_bot_status()
        elif self._matches_pattern(msg, ['account', 'balance', 'equity', 'margin']):
            return self._get_account_info()
        elif self._matches_pattern(msg, ['trades', 'open', 'positions', 'current']):
            return self._get_open_trades()
        elif self._matches_pattern(msg, ['help', 'commands', 'what', 'can']):
            return self._get_help_info()
        else:
            return f"🤖 I'm having trouble connecting to my advanced AI capabilities right now, but I'm still here to help with your trading needs! Try asking about account status, open trades, or bot health.\n\n*Technical note: {error}*"

    def _get_current_account_state(self) -> str:
        """Get current account state for context"""
        try:
            account = mt5.account_info()
            if account:
                return f"Account Balance: ${account.balance:,.2f}, Equity: ${account.equity:,.2f}, Free Margin: ${account.margin_free:,.2f}"
            return "Account information unavailable"
        except:
            return "Account information unavailable"

    def _get_current_bot_state(self) -> str:
        """Get current bot state for context"""
        try:
            positions = mt5.positions_get()
            position_count = len(positions) if positions else 0
            return f"Bot Status: Active, Open Positions: {position_count}, Monitored Pairs: {', '.join(config.SYMBOLS)}"
        except:
            return "Bot status unavailable"

    def _matches_pattern(self, text: str, keywords: list) -> bool:
        """Check if text contains any of the keywords"""
        return any(keyword in text for keyword in keywords)

    def _get_bot_status(self) -> str:
        """Get comprehensive bot status"""
        try:
            # Check MT5 connection
            mt5_status = "🟢 Connected" if mt5.terminal_info() else "🔴 Disconnected"

            # Get recent activity
            recent_logs = self.memory.retrieve_memory(limit=5)
            last_activity = recent_logs[0]['timestamp'].strftime('%H:%M:%S') if recent_logs else "No recent activity"

            # Check database connection
            try:
                trades_count = len(db.get_trade_logs(limit=1))
                db_status = "🟢 Connected"
            except:
                db_status = "🔴 Error"
                trades_count = 0

            return f"""🤖 **NexGen TraderPro Status**

**Core Systems:**
• MT5 Connection: {mt5_status}
• Database: {db_status}
• Memory System: 🟢 Active
• AI Engine: 🟢 Running

**Activity:**
• Last Update: {last_activity}
• Symbols Monitored: {', '.join(config.SYMBOLS)}
• Risk Level: {config.RISK_PERCENT}%

**Quick Stats:**
• Total Trades: {trades_count}
• Active Positions: {len(self._get_open_positions_data())}

All systems operational! 🚀"""
        except Exception as e:
            return f"❌ **Status Check Failed**: {str(e)}"

    def _get_account_info(self) -> str:
        """Get account information"""
        try:
            account = mt5.account_info()
            if not account:
                return "❌ **Account Error**: Unable to retrieve account information"

            return f"""💰 **Account Information**

**Balance & Equity:**
• Balance: ${account.balance:,.2f}
• Equity: ${account.equity:,.2f}
• Profit/Loss: ${account.profit:,.2f}
• Free Margin: ${account.margin_free:,.2f}

**Account Details:**
• Account: {account.login}
• Server: {account.server}
• Currency: {account.currency}
• Leverage: 1:{int(account.leverage)}

**Risk Management:**
• Margin Level: {account.margin_level:.1f}%
• Used Margin: ${account.margin:,.2f}"""
        except Exception as e:
            return f"❌ **Account Error**: {str(e)}"

    def _get_open_positions_data(self) -> list:
        """Helper to get open positions data"""
        try:
            positions = mt5.positions_get()
            return list(positions) if positions else []
        except:
            return []

    def _get_open_trades(self) -> str:
        """Get current open positions"""
        try:
            positions = self._get_open_positions_data()

            if not positions:
                return "📊 **Open Positions**: No active positions currently"

            result = f"📊 **Open Positions** ({len(positions)} active)\n\n"

            for pos in positions:
                direction = "🟢 BUY" if pos.type == 0 else "🔴 SELL"
                profit_color = "🟢" if pos.profit >= 0 else "🔴"

                result += f"""**{pos.symbol}** - {direction}
• Volume: {pos.volume}
• Entry: {pos.price_open}
• Current: {pos.price_current}
• P&L: {profit_color} ${pos.profit:.2f}
• Swap: ${pos.swap:.2f}

"""

            return result.strip()
        except Exception as e:
            return f"❌ **Positions Error**: {str(e)}"

    def _get_recent_trades(self) -> str:
        """Get recent trade history"""
        try:
            trades = db.get_trade_logs(limit=5)

            if not trades:
                return "📈 **Recent Trades**: No trades found"

            result = "📈 **Recent Trades** (Last 5)\n\n"

            for trade in trades:
                status_icon = "✅" if trade.get('outcome') == 'EXECUTED' else "❌"
                profit = trade.get('profit_loss', 0)
                profit_text = f"${profit:.2f}" if profit else "Pending"

                result += f"""**{trade['symbol']}** - {trade['type']} {status_icon}
• Time: {trade['timestamp'].strftime('%m/%d %H:%M')}
• Size: {trade.get('size', 'N/A')}
• P&L: {profit_text}

"""

            return result.strip()
        except Exception as e:
            return f"❌ **Trades Error**: {str(e)}"

    def _get_performance_summary(self) -> str:
        """Get performance metrics"""
        try:
            trades = db.get_trade_logs(limit=20)

            if not trades:
                return "📊 **Performance**: No trade data available"

            executed_trades = [t for t in trades if t.get('outcome') == 'EXECUTED']
            total_profit = sum(t.get('profit_loss', 0) for t in executed_trades if t.get('profit_loss'))

            win_trades = [t for t in executed_trades if t.get('profit_loss', 0) > 0]
            loss_trades = [t for t in executed_trades if t.get('profit_loss', 0) < 0]

            win_rate = (len(win_trades) / len(executed_trades) * 100) if executed_trades else 0

            return f"""📊 **Performance Summary** (Last 20 trades)

**Overall:**
• Total P&L: ${total_profit:.2f}
• Win Rate: {win_rate:.1f}%
• Total Trades: {len(executed_trades)}

**Breakdown:**
• Winning Trades: {len(win_trades)}
• Losing Trades: {len(loss_trades)}
• Pending: {len(trades) - len(executed_trades)}

**Risk Metrics:**
• Current Risk: {config.RISK_PERCENT}%
• Max Positions: {len(config.SYMBOLS)}"""
        except Exception as e:
            return f"❌ **Performance Error**: {str(e)}"

    def _get_market_analysis(self) -> str:
        """Get current market analysis"""
        try:
            # Get recent analysis from memory
            analysis_logs = self.memory.retrieve_memory(type_='LLM', limit=3)

            if not analysis_logs:
                return "🔍 **Market Analysis**: No recent analysis available. Running new analysis..."

            latest = analysis_logs[0]

            return f"""🔍 **Latest Market Analysis**

**Generated:** {latest['timestamp'].strftime('%m/%d %H:%M')}

**Analysis:**
{latest['content']}

**Symbols Monitored:** {', '.join(config.SYMBOLS)}

*For real-time signals, ask me to run a reasoning cycle.*"""
        except Exception as e:
            return f"❌ **Analysis Error**: {str(e)}"

    def _get_risk_assessment(self) -> str:
        """Get risk assessment"""
        try:
            account = mt5.account_info()
            positions = self._get_open_positions_data()

            if not account:
                return "❌ **Risk Error**: Unable to access account data"

            total_exposure = sum(abs(pos.profit) for pos in positions)
            risk_percentage = (total_exposure / account.equity * 100) if account.equity > 0 else 0

            risk_level = "🟢 LOW" if risk_percentage < 5 else "🟡 MEDIUM" if risk_percentage < 15 else "🔴 HIGH"

            return f"""⚠️ **Risk Assessment**

**Current Exposure:**
• Risk Level: {risk_level}
• Exposure: {risk_percentage:.1f}% of equity
• Open Positions: {len(positions)}
• Total Exposure: ${total_exposure:.2f}

**Risk Limits:**
• Max Risk per Trade: {config.RISK_PERCENT}%
• Account Equity: ${account.equity:,.2f}
• Free Margin: ${account.margin_free:,.2f}

**Recommendations:**
• {'✅ Risk within acceptable limits' if risk_percentage < 10 else '⚠️ Consider reducing position sizes'}"""
        except Exception as e:
            return f"❌ **Risk Error**: {str(e)}"

    def _get_weekly_summary(self) -> str:
        """Get weekly summary"""
        try:
            summary = self.summarizer.summarize_week()
            return f"📅 **Weekly Summary**\n\n{summary}"
        except Exception as e:
            return f"❌ **Summary Error**: {str(e)}"

    def _get_trade_explanation(self) -> str:
        """Get explanation for recent trades"""
        try:
            trades = db.get_trade_logs(limit=3)

            if not trades:
                return "🤔 **Trade Explanation**: No recent trades to explain"

            latest_trade = trades[0]

            # Try to get explanation from database
            explanation = db.get_explanation_for_trade(latest_trade['id'])

            if explanation:
                return f"""🤔 **Trade Explanation** - {latest_trade['symbol']}

**Trade Details:**
• Action: {latest_trade['type']}
• Time: {latest_trade['timestamp'].strftime('%m/%d %H:%M')}
• Outcome: {latest_trade.get('outcome', 'Pending')}

**AI Explanation:**
{explanation.get('explanation', 'Explanation processing...')}"""
            else:
                return f"""🤔 **Trade Explanation** - {latest_trade['symbol']}

**Trade Details:**
• Action: {latest_trade['type']}
• Time: {latest_trade['timestamp'].strftime('%m/%d %H:%M')}
• Reason: {latest_trade.get('reason', 'No reason provided')}

*For detailed AI explanation, check the Explainability tab.*"""
        except Exception as e:
            return f"❌ **Explanation Error**: {str(e)}"

    def _get_news_events(self) -> str:
        """Get upcoming news events"""
        try:
            # This would integrate with the news filter
            return """📰 **Economic Calendar**

**Today's Key Events:**
• 08:30 - USD Employment Data
• 10:00 - EUR Inflation Report
• 14:00 - Fed Interest Rate Decision

**Impact Assessment:**
• High impact events may cause increased volatility
• Trading may be paused during major announcements

*Check the News tab for detailed event information.*"""
        except Exception as e:
            return f"❌ **News Error**: {str(e)}"

    def _get_config_info(self) -> str:
        """Get configuration information"""
        try:
            return f"""⚙️ **Configuration Settings**

**Trading Parameters:**
• Risk per Trade: {config.RISK_PERCENT}%
• Take Profit Ratio: {config.TP_RATIO}
• Symbols: {', '.join(config.SYMBOLS)}

**Technical Indicators:**
• RSI Period: {config.RSI_PERIOD}
• MACD Fast: {config.MACD_FAST}
• MACD Slow: {config.MACD_SLOW}
• Bollinger Period: {config.BOLLINGER_PERIOD}

**Account:**
• MT5 Server: {config.MT5_SERVER}
• Account: {config.MT5_LOGIN}

*Configuration can be modified in the Config tab.*"""
        except Exception as e:
            return f"❌ **Config Error**: {str(e)}"

    def modify_config(self, parameter: str, value: Any) -> str:
        """Modify system configuration parameters"""
        try:
            # Map of user-friendly parameter names to config attributes
            param_map = {
                'risk': 'RISK_PERCENT',
                'risk_percent': 'RISK_PERCENT',
                'risk_percentage': 'RISK_PERCENT',
                'take_profit': 'TP_RATIO',
                'tp_ratio': 'TP_RATIO',
                'stop_loss': 'SL_PIPS',
                'sl_pips': 'SL_PIPS',
                'symbols': 'SYMBOLS',
                'currency_pairs': 'SYMBOLS',
                'pairs': 'SYMBOLS',
                'rsi_period': 'RSI_PERIOD',
                'macd_fast': 'MACD_FAST',
                'macd_slow': 'MACD_SLOW',
                'bollinger_period': 'BOLLINGER_PERIOD'
            }

            param_key = param_map.get(parameter.lower())
            if not param_key:
                return f"❌ **Configuration Error**: Unknown parameter '{parameter}'. Available parameters: {', '.join(param_map.keys())}"

            # Validate and convert value based on parameter type
            if param_key in ['RISK_PERCENT', 'TP_RATIO']:
                try:
                    value = float(value)
                    if param_key == 'RISK_PERCENT' and (value <= 0 or value > 10):
                        return "❌ **Validation Error**: Risk percentage must be between 0.1 and 10.0"
                    if param_key == 'TP_RATIO' and (value <= 0 or value > 5):
                        return "❌ **Validation Error**: Take profit ratio must be between 0.1 and 5.0"
                except ValueError:
                    return f"❌ **Validation Error**: {parameter} must be a number"

            elif param_key in ['SL_PIPS', 'RSI_PERIOD', 'MACD_FAST', 'MACD_SLOW', 'BOLLINGER_PERIOD']:
                try:
                    value = int(value)
                    if value <= 0:
                        return f"❌ **Validation Error**: {parameter} must be a positive integer"
                except ValueError:
                    return f"❌ **Validation Error**: {parameter} must be an integer"

            elif param_key == 'SYMBOLS':
                if isinstance(value, str):
                    value = [s.strip().upper() for s in value.split(',')]
                valid_symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
                invalid_symbols = [s for s in value if s not in valid_symbols]
                if invalid_symbols:
                    return f"❌ **Validation Error**: Invalid symbols: {', '.join(invalid_symbols)}. Valid symbols: {', '.join(valid_symbols)}"

            # Update the configuration
            old_value = getattr(config, param_key)
            setattr(config, param_key, value)

            return f"✅ **Configuration Updated**\n\n**Parameter**: {parameter}\n**Old Value**: {old_value}\n**New Value**: {value}\n\n*Changes will take effect on the next trading cycle.*"

        except Exception as e:
            return f"❌ **Configuration Error**: Failed to update {parameter}: {str(e)}"

    def _get_help_info(self) -> str:
        """Get help information"""
        return """🤖 **NexGen AI Assistant - Full Capabilities**

**💬 Natural Conversation:**
I can chat about anything! Ask me about:
• General topics, weather, technology, philosophy
• Trading strategies and market insights
• How my AI systems work
• Personal advice and recommendations

**📊 Trading & Analysis:**
• "What's my account balance?" - Live account data
• "Show open positions" - Current trades
• "How's my performance?" - P&L analysis
• "Market outlook for EURUSD" - Technical analysis
• "Risk assessment" - Current exposure

**⚙️ System Control:**
• "Change risk to 1.5%" - Modify settings
• "Add USDJPY to monitored pairs" - Update symbols
• "Set take profit ratio to 2.0" - Adjust parameters

**🔍 Advanced Features:**
• "Explain your last decision" - Trade reasoning
• "Run market analysis" - Generate fresh signals
• "System state report" - Comprehensive overview
• "Weekly performance summary" - Detailed metrics

**Examples of what you can ask:**
• "Hi, how are you today?"
• "What do you think about the current market?"
• "Check my live account and tell me if I should be worried"
• "Change my risk settings to be more conservative"

Just talk to me naturally - I understand context and can help with anything!"""

# Usage example:
# agent = ChatAgent()
# response = agent.process_message('Summarize the week')