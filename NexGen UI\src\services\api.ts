// API client for NexGen Forex Trading Bot

// Types
export interface ChatRequest {
  message: string;
  use_multi_agent?: boolean;
}

export interface ChatResponse {
  response: string;
  voice_enabled: boolean;
}

export interface MemoryQueryRequest {
  query?: string;
  type_?: string;
  limit?: number;
}

export interface MemoryQueryResponse {
  results: any[];
}

export interface TranscriptionResponse {
  text: string;
  confidence: number;
}

export interface VoiceSettings {
  voice_id?: string;
  speed?: number;
  pitch?: number;
  volume?: number;
}

export interface BacktestResult {
  symbol: string;
  strategy: string;
  timestamp: string;
  performance: {
    total_trades: number;
    win_rate: number;
    profit_factor: number;
    expectancy: number;
    max_drawdown: number;
    final_balance: number;
  };
  equity_curve: Array<{
    time: string;
    equity: number;
  }>;
  trades: Array<{
    type: string;
    price?: number;
    time: string | number;
    pnl?: number;
  }>;
  ai_assessment: string;
}

export interface Config {
    MT5_LOGIN: number;
    MT5_PASSWORD?: string; // Often sensitive, might not be sent
    MT5_SERVER: string;
    MT5_PATH: string;
    RISK_PERCENT: number;
    TP_RATIO: number;
    SYMBOLS: string[];
    OPENAI_API_KEY?: string; // Often sensitive, might not be sent
    RSI_PERIOD: number;
    MACD_FAST: number;
    MACD_SLOW: number;
    MACD_SIGNAL: number;
    BOLLINGER_PERIOD: number;
    BOLLINGER_STD: number;
    STOCHASTIC_PERIOD: number;
    POSTGRES_HOST: string;
    POSTGRES_PORT: number;
    POSTGRES_DB: string;
    POSTGRES_USER: string;
    POSTGRES_PASSWORD?: string; // Often sensitive, might not be sent
    VECTOR_STORE_TYPE: string;
    VECTOR_STORE_PATH: string;
}

export interface TradeExplanation {
    id: number;
    trade_id: number;
    explanation: string;
    created_at: string;
}

export interface NewsEvent {
  id: string;
  time: string;
  currency: string;
  event: string;
  impact: 'low' | 'medium' | 'high';
  forecast?: string;
  previous?: string;
  isBlocked: boolean;
}

export interface AnomalyStatus {
  score: number;
  level: 'low' | 'medium' | 'high';
  timestamp: string;
  indicators: string[];
  confidence: number;
  history: number[];
}

export interface TradeLog {
  id: number;
  timestamp: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  size: number;
  price: number;
  profit_loss: number;
  outcome: 'WIN' | 'LOSS' | 'OPEN' | 'EXECUTED' | 'FAILED';
}

export interface BacktestResultsResponse {
  status: string;
  completed_backtests: BacktestResult[];
  current_backtest?: {
    symbol: string;
    strategy: string;
    progress: number;
    start_time: string;
    estimated_completion: string;
  };
}

// Base API URL - adjust as needed for your environment
const API_BASE_URL = 'http://localhost:8000';

// API Client
export const api = {
  // Chat endpoint
  chat: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to process chat message');
    }

    return response.json();
  },

  // Voice endpoint
  generateVoice: async (text: string, settings?: VoiceSettings): Promise<Blob | null> => {
    // Construct URL with any provided settings
    let url = `${API_BASE_URL}/voice?text=${encodeURIComponent(text)}`;
    
    if (settings) {
      if (settings.voice_id) url += `&voice_id=${settings.voice_id}`;
      if (settings.speed) url += `&speed=${settings.speed}`;
      if (settings.pitch) url += `&pitch=${settings.pitch}`;
      if (settings.volume) url += `&volume=${settings.volume}`;
    }
    
    const response = await fetch(url, {
      method: 'POST',
    });

    if (!response.ok) {
      if (response.headers.get('content-type')?.includes('application/json')) {
        // It's an error response
        const error = await response.json();
        console.error('Voice generation error:', error);
        return null;
      }
      return null;
    }

    // Check if we got audio back
    if (response.headers.get('content-type')?.includes('audio/')) {
      return response.blob();
    }
    
    // Otherwise it's a status message
    console.log('Voice status:', await response.json());
    return null;
  },
  
  // Transcribe audio from a microphone recording
  transcribeAudio: async (audioBlob: Blob): Promise<TranscriptionResponse> => {
    // Create a FormData instance
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.webm');
    
    const response = await fetch(`${API_BASE_URL}/transcribe`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to transcribe audio');
    }
    
    return response.json();
  },
  
  // Get available voice options
  getVoiceOptions: async (): Promise<{voices: Array<{id: string, name: string, preview_url: string}>}> => {
    const response = await fetch(`${API_BASE_URL}/voices`, {
      method: 'GET',
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get voice options');
    }
    
    return response.json();
  },
  
  // Send audio data for streaming transcription (WebSockets)
  startStreamingTranscription: (onTranscription: (text: string, isFinal: boolean) => void, onError: (error: Error) => void) => {
    try {
      const ws = new WebSocket(`ws://${API_BASE_URL.replace('http://', '')}/ws/transcribe`);
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.error) {
          onError(new Error(data.error));
        } else {
          onTranscription(data.text, data.is_final);
        }
      };
      
      ws.onerror = () => {
        onError(new Error('WebSocket error'));
      };
      
      ws.onclose = () => {
        console.log('Transcription WebSocket closed');
      };
      
      // Return methods to send audio data and close connection
      return {
        sendAudio: (audioData: Blob) => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(audioData);
          }
        },
        close: () => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.close();
          }
        }
      };
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to start streaming transcription'));
      return {
        sendAudio: () => {},
        close: () => {}
      };
    }
  },

  // Memory query endpoint
  queryMemory: async (request: MemoryQueryRequest): Promise<MemoryQueryResponse> => {
    const response = await fetch(`${API_BASE_URL}/memory`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to query memory');
    }

    return response.json();
  },

  // Memory summarization endpoint
  summarizeMemory: async (): Promise<{ summary: string }> => {
    const response = await fetch(`${API_BASE_URL}/summarize`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to summarize memory');
    }

    return response.json();
  },

  // Run reasoning cycle
  runReasoning: async (): Promise<{ status: string }> => {
    const response = await fetch(`${API_BASE_URL}/run-reasoning`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to run reasoning cycle');
    }

    return response.json();
  },

  // Run a backtest
  backtest: async (params: { symbol: string; start_date: string; end_date: string; initial_balance?: number }) => {
    const response = await fetch(`${API_BASE_URL}/backtest`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to run backtest');
    }
    return response.json();
  },

  // Get backtest results (for autonomous AI-driven backtesting)
  getBacktestResults: async (): Promise<BacktestResultsResponse> => {
    const response = await fetch(`${API_BASE_URL}/backtest_results`);
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to fetch backtest results');
    }
    return response.json();
  },

  // Get current account stats (forward test/demo/live)
  getAccountStats: async () => {
    const response = await fetch(`${API_BASE_URL}/account_stats`);
    if (!response.ok) throw new Error('Failed to fetch account stats');
    return response.json();
  },

  // Get open trades (live account)
  getOpenTrades: async () => {
    const response = await fetch(`${API_BASE_URL}/open_trades`);
    if (!response.ok) throw new Error('Failed to fetch open trades');
    return response.json();
  },

  // Get demo account stats (for forward testing)
  getDemoAccountStats: async () => {
    const response = await fetch(`${API_BASE_URL}/demo_account_stats`);
    if (!response.ok) throw new Error('Failed to fetch demo account stats');
    return response.json();
  },

  // Get demo open trades (for forward testing)
  getDemoOpenTrades: async () => {
    const response = await fetch(`${API_BASE_URL}/demo_open_trades`);
    if (!response.ok) throw new Error('Failed to fetch demo open trades');
    return response.json();
  },

  // Set mode (backtest/forward)
  setMode: async (mode: 'backtest' | 'forward') => {
    const response = await fetch(`${API_BASE_URL}/set_mode`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ mode }),
    });
    if (!response.ok) throw new Error('Failed to set mode');
    return response.json();
  },

  // Get current mode
  getMode: async () => {
    const response = await fetch(`${API_BASE_URL}/get_mode`);
    if (!response.ok) throw new Error('Failed to get mode');
    return response.json();
  },

  // Get system health metrics
  getSystemHealth: async () => {
    const response = await fetch(`${API_BASE_URL}/system_health`);
    if (!response.ok) throw new Error('Failed to fetch system health');
    return response.json();
  },

  // Get and update configuration
  getConfig: async (): Promise<Config> => {
    const response = await fetch(`${API_BASE_URL}/config`);
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to fetch config');
    }
    return response.json();
  },

  updateConfig: async (config: Partial<Config>): Promise<Config> => {
    const response = await fetch(`${API_BASE_URL}/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to update config');
    }
    return response.json();
  },

  // Get trade explanation
  getTradeExplanation: async (tradeId: number): Promise<TradeExplanation> => {
    const response = await fetch(`${API_BASE_URL}/explanations/${tradeId}`);
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch trade explanation');
    }
    return response.json();
  },

  // Get news events
  getNews: async (): Promise<{ events: NewsEvent[] }> => {
    const response = await fetch(`${API_BASE_URL}/news`);
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch news events');
    }
    return response.json();
  },

  // Get trade logs
  getTradeLogs: async (limit: number = 100): Promise<{ logs: TradeLog[] }> => {
    const response = await fetch(`${API_BASE_URL}/trade_logs?limit=${limit}`);
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch trade logs');
    }
    return response.json();
  },

  // Get anomaly status
  getAnomalyStatus: async (): Promise<AnomalyStatus> => {
      const response = await fetch(`${API_BASE_URL}/anomaly_status`);
      if (!response.ok) {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to fetch anomaly status');
      }
      return response.json();
  },

  // News sentiment endpoint
  getNewsSentiment: async (symbol: string = 'EURUSD'): Promise<{symbol:string;decision:string;score:number;top_article:any}> => {
    const response = await fetch(`${API_BASE_URL}/news_sentiment?symbol=${symbol}`);
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch news sentiment');
    }
    return response.json();
  },

  // Get price data for charts
  getPriceData: async (params: { symbol: string; timeframe: string; limit: number }): Promise<any[]> => {
    const response = await fetch(`${API_BASE_URL}/price_data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to fetch price data');
    }
    return response.json();
  },
};

export default api; 