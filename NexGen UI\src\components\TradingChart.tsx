import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON><PERSON>, ColorType, IChartApi, ISeriesApi, UTCTimestamp, LineStyle, CandlestickSeries, LineSeries, AreaSeries } from 'lightweight-charts';
import { BarChart3, LineChart as LineChartIcon, Grid3X3, TrendingUp, TrendingDown, Minus, X } from 'lucide-react';
import api from '../services/api';

interface CandlestickData {
    time: UTCTimestamp;
    open: number;
    high: number;
    low: number;
    close: number;
}

interface CurrencyPair {
    symbol: string;
    name: string;
    flag: string;
    color: string;
}

interface ChartConfig {
    symbol: string;
    chartType: 'candlestick' | 'line' | 'area';
    timeframe: string;
}

const chartTypes = [
  { key: 'candlestick', label: 'Candles', icon: <BarChart3 size={16} /> },
  { key: 'line', label: 'Line', icon: <LineChartIcon size={16} /> },
  { key: 'area', label: 'Area', icon: <BarChart3 size={16} /> },
];

const currencyPairs: CurrencyPair[] = [
  { symbol: 'EURUSD', name: 'EUR/USD', flag: '🇪🇺🇺🇸', color: '#00d4ff' },
  { symbol: 'GBPUSD', name: 'GBP/USD', flag: '🇬🇧🇺🇸', color: '#00ffb3' },
  { symbol: 'USDJPY', name: 'USD/JPY', flag: '🇺🇸🇯🇵', color: '#ff6b6b' },
  { symbol: 'AUDUSD', name: 'AUD/USD', flag: '🇦🇺🇺🇸', color: '#ffd93d' },
  { symbol: 'USDCAD', name: 'USD/CAD', flag: '🇺🇸🇨🇦', color: '#6c5ce7' },
  { symbol: 'USDCHF', name: 'USD/CHF', flag: '🇺🇸🇨🇭', color: '#fd79a8' },
];

const timeframes = [
  { key: 'M1', label: '1M' },
  { key: 'M5', label: '5M' },
  { key: 'M15', label: '15M' },
  { key: 'M30', label: '30M' },
  { key: 'H1', label: '1H' },
  { key: 'H4', label: '4H' },
  { key: 'D1', label: '1D' },
];

const layoutOptions = [
  { key: '1x1', label: 'Single', cols: 1, rows: 1 },
  { key: '2x1', label: '2x1', cols: 2, rows: 1 },
  { key: '2x2', label: '2x2', cols: 2, rows: 2 },
  { key: '3x2', label: '3x2', cols: 3, rows: 2 },
];

// Single Chart Component
interface SingleChartProps {
  config: ChartConfig;
  pair: CurrencyPair;
  onConfigChange: (config: ChartConfig) => void;
  onRemove: () => void;
  height: number;
  canRemove: boolean;
}

const SingleChart: React.FC<SingleChartProps> = ({ config, pair, onConfigChange, onRemove, height, canRemove }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const [data, setData] = useState<CandlestickData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [priceChange, setPriceChange] = useState<{ value: number; percentage: number } | null>(null);

  // Fetch real price data
  const fetchPriceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.getPriceData({
        symbol: config.symbol,
        timeframe: config.timeframe,
        limit: 100
      });

      if (response && Array.isArray(response)) {
        const formattedData: CandlestickData[] = response.map((item: any) => ({
          time: (typeof item.time === 'number' ? item.time : Math.floor(new Date(item.time).getTime() / 1000)) as UTCTimestamp,
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
        }));

        setData(formattedData);

        // Calculate price change
        if (formattedData.length >= 2) {
          const current = formattedData[formattedData.length - 1].close;
          const previous = formattedData[formattedData.length - 2].close;
          const change = current - previous;
          const percentage = (change / previous) * 100;
          setPriceChange({ value: change, percentage });
        }
      }
    } catch (err: any) {
      console.error(`Error fetching data for ${config.symbol}:`, err);
      setError(err.message || 'Failed to fetch price data');

      // Generate mock data as fallback
      const mockData: CandlestickData[] = [];
      let lastClose = 1.1000 + Math.random() * 0.1;
      for (let i = 0; i < 100; i++) {
        const time = (Math.floor(Date.now() / 1000) - (100 - i) * 900) as UTCTimestamp;
        const open = lastClose + (Math.random() - 0.5) * 0.01;
        const close = open + (Math.random() - 0.5) * 0.02;
        const high = Math.max(open, close) + Math.random() * 0.01;
        const low = Math.min(open, close) - Math.random() * 0.01;
        lastClose = close;
        mockData.push({ time, open, high, low, close });
      }
      setData(mockData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPriceData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchPriceData, 30000);
    return () => clearInterval(interval);
  }, [config.symbol, config.timeframe]);

  useEffect(() => {
    if (!chartContainerRef.current || data.length === 0) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: height - 80, // Account for header
      layout: {
        background: { type: ColorType.Solid, color: '#181c23' },
        textColor: '#c3e7fa',
      },
      grid: {
        vertLines: { color: '#23272f', style: LineStyle.Dotted },
        horzLines: { color: '#23272f', style: LineStyle.Dotted },
      },
      crosshair: { mode: 1 },
      timeScale: { borderColor: '#23272f', timeVisible: true, secondsVisible: false },
      rightPriceScale: { borderColor: '#23272f' },
    });
    chartRef.current = chart;

    let series: ISeriesApi<any>;

    if (config.chartType === 'candlestick') {
      series = chart.addSeries(CandlestickSeries, {
        upColor: '#00ffb3',
        downColor: '#ff4757',
        borderVisible: false,
        wickUpColor: '#00ffb3',
        wickDownColor: '#ff4757',
      });
      series.setData(data);
    } else if (config.chartType === 'line') {
      series = chart.addSeries(LineSeries, {
        color: pair.color,
        lineWidth: 2,
      });
      series.setData(data.map(c => ({ time: c.time, value: c.close })));
    } else {
      series = chart.addSeries(AreaSeries, {
        topColor: `${pair.color}66`,
        bottomColor: `${pair.color}11`,
        lineColor: pair.color,
        lineWidth: 2,
      });
      series.setData(data.map(c => ({ time: c.time, value: c.close })));
    }

    chart.timeScale().fitContent();

    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.resize(chartContainerRef.current.clientWidth, height - 80);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, [config.chartType, data, height, pair.color]);

  const getPriceChangeIcon = () => {
    if (!priceChange) return <Minus size={14} className="text-terminal-muted" />;
    if (priceChange.value > 0) return <TrendingUp size={14} className="text-neon-green" />;
    return <TrendingDown size={14} className="text-neon-red" />;
  };

  const getPriceChangeColor = () => {
    if (!priceChange) return 'text-terminal-muted';
    return priceChange.value > 0 ? 'text-neon-green' : 'text-neon-red';
  };

  return (
    <div className="terminal-card p-3 flex flex-col h-full">
      {/* Chart Header */}
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{pair.flag}</span>
          <div>
            <h3 className="text-sm font-bold text-terminal-text font-mono">{pair.name}</h3>
            {priceChange && (
              <div className={`flex items-center space-x-1 text-xs font-mono ${getPriceChangeColor()}`}>
                {getPriceChangeIcon()}
                <span>{priceChange.percentage >= 0 ? '+' : ''}{priceChange.percentage.toFixed(2)}%</span>
              </div>
            )}
          </div>
        </div>

        {/* Chart Controls */}
        <div className="flex items-center space-x-1">
          {/* Remove Button */}
          {canRemove && (
            <button
              onClick={onRemove}
              className="p-1.5 text-terminal-muted hover:text-neon-red hover:bg-neon-red/10 rounded transition-all duration-200"
              title="Remove this chart"
            >
              <X size={14} />
            </button>
          )}
          {/* Timeframe Selector */}
          <select
            value={config.timeframe}
            onChange={(e) => onConfigChange({ ...config, timeframe: e.target.value })}
            className="text-xs bg-terminal-surface border border-terminal-border rounded px-2 py-1 text-terminal-text font-mono"
          >
            {timeframes.map(tf => (
              <option key={tf.key} value={tf.key}>{tf.label}</option>
            ))}
          </select>

          {/* Chart Type Selector */}
          <select
            value={config.chartType}
            onChange={(e) => onConfigChange({ ...config, chartType: e.target.value as any })}
            className="text-xs bg-terminal-surface border border-terminal-border rounded px-2 py-1 text-terminal-text font-mono"
          >
            {chartTypes.map(type => (
              <option key={type.key} value={type.key}>{type.label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1 relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-terminal-bg/80 rounded-lg">
            <div className="text-neon-blue font-mono text-sm">Loading {pair.name}...</div>
          </div>
        )}
        {error && !loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-terminal-bg rounded-lg">
            <div className="text-neon-red font-mono text-xs text-center">
              <div>Error loading {pair.name}</div>
              <div className="text-terminal-muted mt-1">Using mock data</div>
            </div>
          </div>
        )}
        <div ref={chartContainerRef} className="w-full h-full rounded-lg border border-terminal-border bg-terminal-bg" />
      </div>
    </div>
  );
};

// Main Multi-Currency Trading Chart Component
const TradingChart: React.FC = () => {
  const [layout, setLayout] = useState<'1x1' | '2x1' | '2x2' | '3x2'>('2x2');
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>([
    { symbol: 'EURUSD', chartType: 'candlestick', timeframe: 'H1' },
    { symbol: 'GBPUSD', chartType: 'candlestick', timeframe: 'H1' },
    { symbol: 'USDJPY', chartType: 'line', timeframe: 'H1' },
    { symbol: 'AUDUSD', chartType: 'area', timeframe: 'H1' },
  ]);

  const currentLayout = layoutOptions.find(l => l.key === layout)!;
  const maxCharts = currentLayout.cols * currentLayout.rows;
  const visibleConfigs = chartConfigs.slice(0, maxCharts);

  const updateChartConfig = (index: number, newConfig: ChartConfig) => {
    const newConfigs = [...chartConfigs];
    newConfigs[index] = newConfig;
    setChartConfigs(newConfigs);
  };

  const addChart = () => {
    if (chartConfigs.length < 6) {
      const availablePairs = currencyPairs.filter(
        pair => !chartConfigs.some(config => config.symbol === pair.symbol)
      );
      if (availablePairs.length > 0) {
        setChartConfigs([
          ...chartConfigs,
          { symbol: availablePairs[0].symbol, chartType: 'candlestick', timeframe: 'H1' }
        ]);
      }
    }
  };

  const removeChart = (index: number) => {
    if (chartConfigs.length > 1) {
      const newConfigs = chartConfigs.filter((_, i) => i !== index);
      setChartConfigs(newConfigs);
    }
  };

  const getChartHeight = () => {
    const baseHeight = 400;
    return Math.floor(baseHeight / currentLayout.rows);
  };

  return (
    <div className="terminal-card p-6 flex flex-col space-y-4">
      {/* Header Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-terminal-text font-mono flex items-center space-x-2">
          <BarChart3 size={24} className="text-neon-blue" />
          <span>Multi-Currency Charts</span>
        </h2>

        <div className="flex items-center space-x-4">
          {/* Layout Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-terminal-muted font-mono">Layout:</span>
            <div className="flex bg-terminal-surface rounded-lg p-1 border border-terminal-border">
              {layoutOptions.map((option) => (
                <button
                  key={option.key}
                  className={`flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-mono font-medium transition-all duration-200 ${
                    layout === option.key
                      ? 'bg-neon-blue text-white shadow-glow-blue-sm'
                      : 'text-terminal-muted hover:text-terminal-text'
                  }`}
                  onClick={() => setLayout(option.key as any)}
                >
                  <Grid3X3 size={12} />
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Add/Remove Chart Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={addChart}
              disabled={chartConfigs.length >= 6}
              className="px-3 py-1.5 bg-neon-green/20 text-neon-green border border-neon-green/30 rounded-lg text-xs font-mono hover:bg-neon-green/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              + Add Chart
            </button>
            <button
              onClick={() => removeChart(chartConfigs.length - 1)}
              disabled={chartConfigs.length <= 1}
              className="px-3 py-1.5 bg-neon-red/20 text-neon-red border border-neon-red/30 rounded-lg text-xs font-mono hover:bg-neon-red/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              - Remove Last
            </button>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div
        className={`grid gap-4`}
        style={{
          gridTemplateColumns: `repeat(${currentLayout.cols}, 1fr)`,
          gridTemplateRows: `repeat(${currentLayout.rows}, 1fr)`,
        }}
      >
        {visibleConfigs.map((config, index) => {
          const pair = currencyPairs.find(p => p.symbol === config.symbol)!;
          return (
            <SingleChart
              key={`${config.symbol}-${index}`}
              config={config}
              pair={pair}
              onConfigChange={(newConfig) => updateChartConfig(index, newConfig)}
              onRemove={() => removeChart(index)}
              height={getChartHeight()}
              canRemove={chartConfigs.length > 1}
            />
          );
        })}
      </div>

      {/* Chart Summary */}
      <div className="flex justify-between items-center text-xs text-terminal-muted font-mono border-t border-terminal-border pt-4">
        <div>
          Showing {visibleConfigs.length} of {chartConfigs.length} charts • {layout} layout
        </div>
        <div className="flex items-center space-x-4">
          <span>🟢 Real-time data</span>
          <span>📊 Auto-refresh: 30s</span>
          <span>⚡ MT5 Integration</span>
        </div>
      </div>
    </div>
  );
};

export default TradingChart;