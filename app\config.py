import os
from dotenv import load_dotenv
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

class Config(BaseModel):
    # MT5 Demo Account Configuration (for forward testing, backtesting)
    MT5_DEMO_LOGIN: int = int(os.getenv('MT5_DEMO_LOGIN', os.getenv('MT5_LOGIN', 0)))
    MT5_DEMO_PASSWORD: str = os.getenv('MT5_DEMO_PASSWORD', os.getenv('MT5_PASSWORD', ''))
    MT5_DEMO_SERVER: str = os.getenv('MT5_DEMO_SERVER', os.getenv('MT5_SERVER', ''))

    # MT5 Live Account Configuration (for real trading and account stats)
    MT5_LIVE_LOGIN: int = int(os.getenv('MT5_LIVE_LOGIN', os.getenv('MT5_LOGIN', 0)))
    MT5_LIVE_PASSWORD: str = os.getenv('MT5_LIVE_PASSWORD', os.getenv('MT5_PASSWORD', ''))
    MT5_LIVE_SERVER: str = os.getenv('MT5_LIVE_SERVER', os.getenv('MT5_SERVER', ''))

    # MT5 Path (same for both accounts)
    MT5_PATH: str = os.getenv('MT5_PATH', 'C:/Program Files/MetaTrader 5/terminal64.exe')

    # Legacy support (fallback to demo if not specified)
    MT5_LOGIN: int = int(os.getenv('MT5_LOGIN', 0))
    MT5_PASSWORD: str = os.getenv('MT5_PASSWORD', '')
    MT5_SERVER: str = os.getenv('MT5_SERVER', '')
    
    # Trading Parameters
    RISK_PERCENT: float = float(os.getenv('RISK_PERCENT', 2.0))
    TP_RATIO: float = float(os.getenv('TP_RATIO', 1.5))
    SYMBOLS: list[str] = os.getenv('SYMBOLS', 'EURUSD,GBPUSD').split(',')
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '')
    
    # Finnhub Configuration
    FINNHUB_API_KEY: str = os.getenv('FINNHUB_API_KEY', '')
    
    # Technical Indicators
    RSI_PERIOD: int = 14
    MACD_FAST: int = 12
    MACD_SLOW: int = 26
    MACD_SIGNAL: int = 9
    BOLLINGER_PERIOD: int = 20
    BOLLINGER_STD: int = 2
    STOCHASTIC_PERIOD: int = 14

    # PostgreSQL Configuration
    POSTGRES_HOST: str = os.getenv('POSTGRES_HOST', 'localhost')
    POSTGRES_PORT: int = int(os.getenv('POSTGRES_PORT', 5432))
    POSTGRES_DB: str = os.getenv('POSTGRES_DB', 'forex_memory')
    POSTGRES_USER: str = os.getenv('POSTGRES_USER', 'postgres')
    POSTGRES_PASSWORD: str = os.getenv('POSTGRES_PASSWORD', '')

    # Vector Store Configuration
    VECTOR_STORE_TYPE: str = os.getenv('VECTOR_STORE_TYPE', 'chromadb')  # or 'faiss'
    VECTOR_STORE_PATH: str = os.getenv('VECTOR_STORE_PATH', 'vector_store')

    # News Source Configuration
    NEWS_SOURCE: str = os.getenv('NEWS_SOURCE', 'mt5')  # 'mt5' or 'finnhub'
    MT5_CALENDAR_URL: str = os.getenv('MT5_CALENDAR_URL', 'https://ec.forextime.com/calendar')

    # Local MT5 EA calendar JSON path (optional)
    CALENDAR_FILE_PATH: str = os.getenv('CALENDAR_FILE_PATH', '')

# Initialize configuration
config = Config() 