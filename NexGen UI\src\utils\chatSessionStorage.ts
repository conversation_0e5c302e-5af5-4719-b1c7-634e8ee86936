// Chat session storage utility for managing multiple chat conversations
export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  message: string;
  timestamp: string;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
}

const STORAGE_KEY = 'nexgen_chat_sessions';
const CURRENT_SESSION_KEY = 'nexgen_current_session';

export class ChatSessionStorage {
  private static instance: ChatSessionStorage;
  
  static getInstance(): ChatSessionStorage {
    if (!ChatSessionStorage.instance) {
      ChatSessionStorage.instance = new ChatSessionStorage();
    }
    return ChatSessionStorage.instance;
  }

  // Get all chat sessions
  getAllSessions(): ChatSession[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading chat sessions:', error);
      return [];
    }
  }

  // Get a specific session by ID
  getSession(sessionId: string): ChatSession | null {
    const sessions = this.getAllSessions();
    return sessions.find(session => session.id === sessionId) || null;
  }

  // Get current active session ID
  getCurrentSessionId(): string | null {
    return localStorage.getItem(CURRENT_SESSION_KEY);
  }

  // Set current active session ID
  setCurrentSessionId(sessionId: string): void {
    localStorage.setItem(CURRENT_SESSION_KEY, sessionId);
  }

  // Create a new chat session
  createSession(title?: string): ChatSession {
    const now = new Date().toISOString();
    const newSession: ChatSession = {
      id: this.generateSessionId(),
      title: title || `Chat ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
      messages: [],
      createdAt: now,
      updatedAt: now
    };

    const sessions = this.getAllSessions();
    sessions.unshift(newSession); // Add to beginning
    this.saveSessions(sessions);
    this.setCurrentSessionId(newSession.id);
    
    return newSession;
  }

  // Update session title
  updateSessionTitle(sessionId: string, title: string): void {
    const sessions = this.getAllSessions();
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);
    
    if (sessionIndex !== -1) {
      sessions[sessionIndex].title = title;
      sessions[sessionIndex].updatedAt = new Date().toISOString();
      this.saveSessions(sessions);
    }
  }

  // Add message to session
  addMessage(sessionId: string, message: ChatMessage): void {
    const sessions = this.getAllSessions();
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);
    
    if (sessionIndex !== -1) {
      sessions[sessionIndex].messages.push(message);
      sessions[sessionIndex].updatedAt = new Date().toISOString();
      
      // Auto-generate title from first user message if it's still the default
      if (sessions[sessionIndex].messages.length === 1 && 
          message.type === 'user' && 
          sessions[sessionIndex].title.startsWith('Chat ')) {
        const shortTitle = message.message.length > 50 
          ? message.message.substring(0, 50) + '...'
          : message.message;
        sessions[sessionIndex].title = shortTitle;
      }
      
      this.saveSessions(sessions);
    }
  }

  // Delete a session
  deleteSession(sessionId: string): void {
    const sessions = this.getAllSessions();
    const filteredSessions = sessions.filter(s => s.id !== sessionId);
    this.saveSessions(filteredSessions);
    
    // If we deleted the current session, switch to the most recent one
    if (this.getCurrentSessionId() === sessionId) {
      if (filteredSessions.length > 0) {
        this.setCurrentSessionId(filteredSessions[0].id);
      } else {
        localStorage.removeItem(CURRENT_SESSION_KEY);
      }
    }
  }

  // Clear all sessions
  clearAllSessions(): void {
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(CURRENT_SESSION_KEY);
  }

  // Get or create current session
  getCurrentSession(): ChatSession {
    const currentId = this.getCurrentSessionId();
    
    if (currentId) {
      const session = this.getSession(currentId);
      if (session) {
        return session;
      }
    }
    
    // Create new session if none exists or current is invalid
    return this.createSession();
  }

  private saveSessions(sessions: ChatSession[]): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving chat sessions:', error);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Export sessions for backup
  exportSessions(): string {
    return JSON.stringify(this.getAllSessions(), null, 2);
  }

  // Import sessions from backup
  importSessions(data: string): boolean {
    try {
      const sessions = JSON.parse(data) as ChatSession[];
      this.saveSessions(sessions);
      return true;
    } catch (error) {
      console.error('Error importing sessions:', error);
      return false;
    }
  }

  // Get session statistics
  getStats() {
    const sessions = this.getAllSessions();
    const totalMessages = sessions.reduce((sum, session) => sum + session.messages.length, 0);
    
    return {
      totalSessions: sessions.length,
      totalMessages,
      oldestSession: sessions.length > 0 ? sessions[sessions.length - 1].createdAt : null,
      newestSession: sessions.length > 0 ? sessions[0].createdAt : null
    };
  }
}

export const chatStorage = ChatSessionStorage.getInstance();
