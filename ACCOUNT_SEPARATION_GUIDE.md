# 🏦 Account Separation Guide

## Overview

The NexGen Forex Trading Bot now supports **separate MT5 accounts** for different purposes:

- **Demo Account**: For safe testing, backtesting, and forward testing
- **Live Account**: For real trading and account monitoring

This separation ensures you never risk real money during testing while still having access to live account data for monitoring.

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Demo Account (for testing)
MT5_DEMO_LOGIN=***********
MT5_DEMO_PASSWORD=your_demo_password
MT5_DEMO_SERVER=MetaQuotes-Demo

# Live Account (for real trading)
MT5_LIVE_LOGIN=your_live_login
MT5_LIVE_PASSWORD=your_live_password
MT5_LIVE_SERVER=your_live_server

# Legacy support (fallback)
MT5_LOGIN=***********
MT5_PASSWORD=your_demo_password
MT5_SERVER=MetaQuotes-Demo
```

## 📊 Account Usage by Feature

### Demo Account Used For:
- ✅ **Forward Testing Panel**: Safe testing environment
- ✅ **Backtesting**: Historical strategy testing
- ✅ **Chart Data**: Price data for analysis
- ✅ **Strategy Development**: AI model training
- ✅ **Bot Trading Cycles**: Automated trading tests

### Live Account Used For:
- ✅ **Account Tab**: Real balance/equity monitoring
- ✅ **Live Trading**: Actual trade execution (when enabled)
- ✅ **Account Statistics**: Real margin, P&L tracking
- ✅ **Open Trades**: Live position monitoring

## 🔌 API Endpoints

### Live Account Endpoints:
- `GET /account_stats` - Live account statistics
- `GET /open_trades` - Live open positions

### Demo Account Endpoints:
- `GET /demo_account_stats` - Demo account statistics
- `GET /demo_open_trades` - Demo open positions

## 🎯 Frontend Integration

### Account Tab
- **Primary**: Uses live account data (`/account_stats`)
- **Fallback**: Uses demo account if live fails
- **Final Fallback**: Uses mock data if both fail

### Forward Test Panel
- **Uses**: Demo account data (`/demo_account_stats`)
- **Purpose**: Safe testing without real money risk

## 🔄 Connection Management

The `MT5Manager` class handles automatic switching between accounts:

```python
# Automatically connects to appropriate account
live_data = mt5_manager.get_account_info('live')
demo_data = mt5_manager.get_account_info('demo')
```

## 🚀 Benefits

1. **Safety**: Never risk real money during testing
2. **Flexibility**: Test strategies on demo while monitoring live account
3. **Separation**: Clear distinction between testing and live operations
4. **Fallback**: Graceful degradation if one account fails
5. **Development**: Safe environment for AI model training

## 🔧 Setup Instructions

1. **Get Demo Account**: 
   - Open demo account with your broker
   - Note login, password, and server

2. **Get Live Account**:
   - Use your existing live trading account
   - Note login, password, and server

3. **Update .env**:
   - Add both account credentials
   - Test connections

4. **Verify**:
   - Check Account tab shows live data
   - Check Forward Test tab shows demo data

## ⚠️ Important Notes

- **Demo Account**: Used for all testing and development
- **Live Account**: Only for real trading and monitoring
- **Never Mix**: Testing always uses demo, trading uses live
- **Fallback**: System gracefully handles connection failures
- **Security**: Keep credentials secure and separate

## 🔍 Troubleshooting

### If Live Account Fails:
- Account tab will show demo data with warning
- Check live account credentials
- Verify live account server is correct

### If Demo Account Fails:
- Forward testing will show error
- Backtesting may fail
- Check demo account credentials

### If Both Fail:
- Account tab shows mock data
- Forward testing shows error
- Check MT5 installation and network

## 📈 Status Indicators

The UI shows connection status:
- 🟢 **Live**: Connected to live account
- 🟡 **Demo**: Using demo account data
- 🔴 **Offline**: Using mock data

This ensures you always know which account type you're viewing!
