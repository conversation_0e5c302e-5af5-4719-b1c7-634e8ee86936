"""
MT5 Connection Manager
Handles separate connections for demo and live accounts
"""
import MetaTrader5 as mt5
from app.config import config
from typing import Literal, Optional
import threading
import time

AccountType = Literal['demo', 'live']

class MT5Manager:
    """Manages separate MT5 connections for demo and live accounts"""
    
    def __init__(self):
        self._demo_connected = False
        self._live_connected = False
        self._current_account = None
        self._connection_lock = threading.Lock()
    
    def connect_demo(self) -> bool:
        """Connect to demo account for forward testing and backtesting"""
        with self._connection_lock:
            try:
                # Shutdown any existing connection
                mt5.shutdown()
                
                success = mt5.initialize(
                    path=config.MT5_PATH,
                    login=config.MT5_DEMO_LOGIN,
                    password=config.MT5_DEMO_PASSWORD,
                    server=config.MT5_DEMO_SERVER
                )
                
                if success:
                    self._demo_connected = True
                    self._live_connected = False
                    self._current_account = 'demo'
                    print(f"✅ Connected to MT5 Demo Account: {config.MT5_DEMO_LOGIN}")
                    return True
                else:
                    print(f"❌ Failed to connect to MT5 Demo Account: {mt5.last_error()}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error connecting to demo account: {e}")
                return False
    
    def connect_live(self) -> bool:
        """Connect to live account for real trading and account stats"""
        with self._connection_lock:
            try:
                # Shutdown any existing connection
                mt5.shutdown()
                
                success = mt5.initialize(
                    path=config.MT5_PATH,
                    login=config.MT5_LIVE_LOGIN,
                    password=config.MT5_LIVE_PASSWORD,
                    server=config.MT5_LIVE_SERVER
                )
                
                if success:
                    self._live_connected = True
                    self._demo_connected = False
                    self._current_account = 'live'
                    print(f"✅ Connected to MT5 Live Account: {config.MT5_LIVE_LOGIN}")
                    return True
                else:
                    print(f"❌ Failed to connect to MT5 Live Account: {mt5.last_error()}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error connecting to live account: {e}")
                return False
    
    def ensure_connection(self, account_type: AccountType) -> bool:
        """Ensure we're connected to the specified account type"""
        if account_type == 'demo':
            if self._current_account != 'demo' or not self._demo_connected:
                return self.connect_demo()
            return True
        elif account_type == 'live':
            if self._current_account != 'live' or not self._live_connected:
                return self.connect_live()
            return True
        return False
    
    def get_account_info(self, account_type: AccountType) -> Optional[dict]:
        """Get account information for specified account type"""
        if not self.ensure_connection(account_type):
            return None
        
        try:
            info = mt5.account_info()
            return info._asdict() if info else None
        except Exception as e:
            print(f"❌ Error getting account info: {e}")
            return None
    
    def get_open_trades(self, account_type: AccountType) -> Optional[list]:
        """Get open trades for specified account type"""
        if not self.ensure_connection(account_type):
            return None
        
        try:
            trades = mt5.positions_get()
            return [t._asdict() for t in trades] if trades else []
        except Exception as e:
            print(f"❌ Error getting open trades: {e}")
            return None
    
    def get_price_data(self, symbol: str, timeframe, num_candles: int, account_type: AccountType = 'demo'):
        """Get price data using specified account type"""
        if not self.ensure_connection(account_type):
            raise ConnectionError(f"Failed to connect to {account_type} account")
        
        try:
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, num_candles)
            if rates is None:
                raise ValueError(f"No data returned for {symbol}")
            return rates
        except Exception as e:
            print(f"❌ Error getting price data: {e}")
            raise
    
    def execute_trade(self, symbol: str, action: str, volume: float, account_type: AccountType = 'live'):
        """Execute trade on specified account type"""
        if not self.ensure_connection(account_type):
            raise ConnectionError(f"Failed to connect to {account_type} account")
        
        # Trade execution logic would go here
        # This is a placeholder for the actual implementation
        print(f"🔄 Executing {action} trade for {symbol} on {account_type} account")
        return {"status": "executed", "account_type": account_type}
    
    def get_current_account_type(self) -> Optional[AccountType]:
        """Get currently connected account type"""
        return self._current_account
    
    def is_connected(self, account_type: Optional[AccountType] = None) -> bool:
        """Check if connected to specified account type or any account"""
        if account_type is None:
            return self._demo_connected or self._live_connected
        elif account_type == 'demo':
            return self._demo_connected and self._current_account == 'demo'
        elif account_type == 'live':
            return self._live_connected and self._current_account == 'live'
        return False
    
    def shutdown(self):
        """Shutdown MT5 connection"""
        with self._connection_lock:
            mt5.shutdown()
            self._demo_connected = False
            self._live_connected = False
            self._current_account = None
            print("🔌 MT5 connection shutdown")

# Global instance
mt5_manager = MT5Manager()
