import { useState, useEffect } from 'react';
import api from '../services/api';

interface Trade {
  symbol: string;
  type: number;
  volume: number;
  price_open: number;
  profit?: number;
  time_open?: string;
  time_open_formatted?: string;
  margin?: number;
  swap?: number;
  commission?: number;
  unrealized_pnl?: number;
  current_price?: number;
}

interface AccountStats {
  balance?: number;
  equity?: number;
  free_margin?: number;
  margin?: number;
  margin_level?: number;
  profit?: number;
  last_updated?: string;
}

const ForwardTestPanel: React.FC = () => {
  const [account, setAccount] = useState<AccountStats | null>(null);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [botStatus, setBotStatus] = useState<string>('monitoring');
  const [lastDecision, setLastDecision] = useState<string | null>(null);
  const [aiDecisions, setAiDecisions] = useState<Array<{timestamp: string; message: string; type: string}>>([]);

  // Fetch forward test data (using demo account)
  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const acc = await api.getDemoAccountStats();
      const trs = await api.getDemoOpenTrades();
      const decisions = await api.getAIDecisions();

      // Set real AI decisions
      setAiDecisions(decisions.decisions);

      // Determine bot status based on recent decisions
      if (decisions.decisions.length > 0) {
        const recentDecision = decisions.decisions[0];
        if (recentDecision.type === 'execution') {
          setBotStatus('trading');
        } else if (recentDecision.type === 'error') {
          setBotStatus('error');
        } else {
          setBotStatus('monitoring');
        }
        setLastDecision(recentDecision.timestamp);
      } else {
        setBotStatus('monitoring');
        setLastDecision(new Date().toISOString());
      }

      setAccount(acc);
      setTrades(trs);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount and every 30 seconds
  useEffect(() => {
    fetchData();
    
    // Poll for updates
    const interval = setInterval(() => {
      fetchData();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Render status badge based on bot status
  const renderStatusBadge = () => {
    switch (botStatus) {
      case 'trading':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-green rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-neon-green font-mono">Trading Active</span>
          </div>
        );
      case 'monitoring':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-blue rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-neon-blue font-mono">Market Monitoring</span>
          </div>
        );
      case 'paused':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-yellow rounded-full"></div>
            <span className="text-sm font-medium text-neon-yellow font-mono">Trading Paused</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-red rounded-full"></div>
            <span className="text-sm font-medium text-neon-red font-mono">Error</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-purple rounded-full"></div>
            <span className="text-sm font-medium text-neon-purple font-mono">{botStatus}</span>
          </div>
        );
    }
  };

  return (
    <div className="terminal-card p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-terminal-text">AI Forward Testing</h2>
        {renderStatusBadge()}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-bold text-terminal-text">Bot Activity</h3>
          <button onClick={fetchData} className="px-4 py-2 rounded-lg bg-terminal-surface text-terminal-muted border border-terminal-border font-mono hover:bg-terminal-card/50 transition-all duration-300" disabled={loading}>
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
        <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
          {loading ? (
            <div className="text-neon-blue font-mono">Loading data...</div>
          ) : error ? (
            <div className="text-neon-red font-mono">{error}</div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Current Mode:</span>
                <span className="text-neon-green">Forward Testing (Demo Account)</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Last Decision:</span>
                <span className="text-terminal-text">{lastDecision ? new Date(lastDecision).toLocaleString() : 'N/A'}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Open Positions:</span>
                <span className="text-terminal-text">{trades.length}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">AI Status:</span>
                <span className="text-neon-purple">Autonomously Managing Trades</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {account && (
        <div className="mb-6">
          <h3 className="text-lg font-bold text-terminal-text mb-2">Account Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
              <div className="text-xs text-terminal-muted font-mono mb-1">Balance</div>
              <div className="text-2xl font-bold text-neon-blue font-mono">${account.balance?.toFixed(2)}</div>
              <div className="text-xs text-terminal-muted font-mono mt-1">
                Last updated: {account.last_updated ? new Date(account.last_updated).toLocaleTimeString() : 'N/A'}
              </div>
            </div>
            <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
              <div className="text-xs text-terminal-muted font-mono mb-1">Equity</div>
              <div className="text-2xl font-bold text-neon-green font-mono">${account.equity?.toFixed(2)}</div>
            </div>
            <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
              <div className="text-xs text-terminal-muted font-mono mb-1">Free Margin</div>
              <div className="text-2xl font-bold text-neon-yellow font-mono">${account.free_margin?.toFixed(2) || '0.00'}</div>
            </div>
            <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
              <div className="text-xs text-terminal-muted font-mono mb-1">Margin Used</div>
              <div className="text-2xl font-bold text-neon-purple font-mono">${account.margin?.toFixed(2) || '0.00'}</div>
            </div>
            <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
              <div className="text-xs text-terminal-muted font-mono mb-1">Margin Level</div>
              <div className="text-2xl font-bold text-neon-orange font-mono">{account.margin_level?.toFixed(0) || '0'}%</div>
            </div>
          </div>
        </div>
      )}

      <div>
        <h3 className="text-lg font-bold text-terminal-text mb-2">Open Trades</h3>
        {trades.length > 0 ? (
          <div className="bg-terminal-bg border border-terminal-border rounded-lg p-4 overflow-x-auto">
            <table className="w-full text-xs font-mono">
              <thead>
                <tr>
                  <th className="px-2 py-1">Symbol</th>
                  <th className="px-2 py-1">Type</th>
                  <th className="px-2 py-1">Volume</th>
                  <th className="px-2 py-1">Open Price</th>
                  <th className="px-2 py-1">Current Price</th>
                  <th className="px-2 py-1">Open Time</th>
                  <th className="px-2 py-1">Margin</th>
                  <th className="px-2 py-1">Profit</th>
                </tr>
              </thead>
              <tbody>
                {trades.map((t, i) => (
                  <tr key={i}>
                    <td className="px-2 py-1 font-bold">{t.symbol}</td>
                    <td className={`px-2 py-1 font-bold ${t.type === 0 ? 'text-neon-green' : 'text-neon-red'}`}>
                      {t.type === 0 ? 'BUY' : 'SELL'}
                    </td>
                    <td className="px-2 py-1">{t.volume}</td>
                    <td className="px-2 py-1">{t.price_open?.toFixed(5) || 'N/A'}</td>
                    <td className="px-2 py-1">{t.current_price?.toFixed(5) || 'N/A'}</td>
                    <td className="px-2 py-1">
                      {t.time_open_formatted || (t.time_open ? new Date(t.time_open).toLocaleString() : 'N/A')}
                    </td>
                    <td className="px-2 py-1 text-neon-purple">
                      ${t.margin?.toFixed(2) || '0.00'}
                    </td>
                    <td className={`px-2 py-1 font-bold ${(t.unrealized_pnl || t.profit || 0) >= 0 ? 'text-neon-green' : 'text-neon-red'}`}>
                      ${(t.unrealized_pnl || t.profit || 0).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="bg-terminal-bg border border-terminal-border rounded-lg p-6 text-center">
            <div className="text-terminal-muted font-mono mb-2">No open trades</div>
            <div className="text-xs text-terminal-muted font-mono">The AI is currently monitoring the market for opportunities</div>
          </div>
        )}
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-bold text-terminal-text mb-2">AI Decision Log</h3>
        <div className="bg-terminal-bg border border-terminal-border rounded-lg p-4">
          {aiDecisions.length > 0 ? (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {aiDecisions.slice(0, 10).map((decision, index) => (
                <div key={index} className="flex items-start space-x-3 p-2 border-b border-terminal-border last:border-b-0">
                  <div className={`w-2 h-2 rounded-full mt-1.5 ${
                    decision.type === 'execution' ? 'bg-neon-green' :
                    decision.type === 'decision' ? 'bg-neon-blue' :
                    decision.type === 'monitoring' ? 'bg-neon-purple' :
                    decision.type === 'safety' ? 'bg-neon-yellow' :
                    decision.type === 'error' ? 'bg-neon-red' :
                    'bg-neon-blue'
                  }`}></div>
                  <div className="flex-1">
                    <div className="text-xs text-terminal-muted font-mono">{decision.timestamp}</div>
                    <div className="text-sm text-terminal-text font-mono">{decision.message}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-terminal-muted font-mono mb-2">No AI decisions logged yet</div>
              <div className="text-xs text-terminal-muted font-mono">
                The AI will start logging decisions once the trading bot is active
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForwardTestPanel; 