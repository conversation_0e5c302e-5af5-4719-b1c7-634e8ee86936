import pandas as pd
from datetime import datetime
from app.data_feeder import DataFeeder
from app.ai_brain import MLBrain, HybridBrain
from app.database import db
from app.postgresql_memory import PostgresMemory
from app.config import config

class Backtester:
    def __init__(self, symbol: str, start_date: str, end_date: str, initial_balance: float = 10000.0):
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.equity_curve = []
        self.trades = []
        self.performance = {}
        self.feeder = DataFeeder('demo')  # Use demo account for backtesting
        self.ml_brain = MLBrain()
        self.hybrid_brain = HybridBrain(self.ml_brain)
        self.memory = PostgresMemory()

    def run(self):
        df = self._fetch_historical_data()
        if df is None or df.empty:
            return None
        position = None
        entry_price = 0
        for i in range(len(df)):
            row = df.iloc[:i+1]
            if len(row) < 30:
                continue
            decision = self.hybrid_brain.get_decision(self.symbol, row)
            price = row.iloc[-1]['close']
            timestamp = row.index[-1]
            if decision.action == 'BUY' and position is None:
                position = 'LONG'
                entry_price = price
                entry_time = timestamp
                self.trades.append({'type': 'BUY', 'price': float(price), 'time': str(timestamp)})
            elif decision.action == 'SELL' and position == 'LONG':
                pnl = price - entry_price
                self.balance += pnl
                self.trades.append({'type': 'SELL', 'price': float(price), 'time': str(timestamp), 'pnl': float(pnl)})
                position = None
            elif decision.action == 'SELL' and position is None:
                position = 'SHORT'
                entry_price = price
                entry_time = timestamp
                self.trades.append({'type': 'SELL', 'price': float(price), 'time': str(timestamp)})
            elif decision.action == 'BUY' and position == 'SHORT':
                pnl = entry_price - price
                self.balance += pnl
                self.trades.append({'type': 'BUY', 'price': float(price), 'time': str(timestamp), 'pnl': float(pnl)})
                position = None
            self.equity_curve.append({'time': str(timestamp), 'equity': float(self.balance)})
        self._calculate_performance()
        self._store_results()
        return {
            'trades': self.trades,
            'equity_curve': self.equity_curve,
            'performance': self.performance
        }

    def _fetch_historical_data(self):
        df = self.feeder.fetch_data(self.symbol, num_candles=2000)
        df = self.feeder.calculate_indicators(df)
        df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]
        return df

    def _calculate_performance(self):
        trades = [t for t in self.trades if 'pnl' in t]
        total_trades = len(trades)
        wins = [t for t in trades if t['pnl'] > 0]
        losses = [t for t in trades if t['pnl'] <= 0]
        win_rate = len(wins) / total_trades if total_trades > 0 else 0
        profit = sum(t['pnl'] for t in trades if t['pnl'] > 0)
        loss = abs(sum(t['pnl'] for t in trades if t['pnl'] < 0))
        profit_factor = profit / loss if loss > 0 else float('inf')
        expectancy = (win_rate * (profit / len(wins) if wins else 0)) - ((1 - win_rate) * (loss / len(losses) if losses else 0))
        max_drawdown = self._max_drawdown([e['equity'] for e in self.equity_curve])
        self.performance = {
            'total_trades': int(total_trades),
            'win_rate': float(win_rate),
            'profit_factor': float(profit_factor) if profit_factor != float('inf') else 999999.0,
            'expectancy': float(expectancy),
            'max_drawdown': float(max_drawdown),
            'final_balance': float(self.balance)
        }

    def _max_drawdown(self, equity_curve):
        max_dd = 0
        peak = equity_curve[0] if equity_curve else 0
        for x in equity_curve:
            if x > peak:
                peak = x
            dd = (peak - x)
            if dd > max_dd:
                max_dd = dd
        return max_dd

    def _store_results(self):
        # Store summary in Postgres memory
        self.memory.store_memory(
            'BACKTEST',
            f'Backtest for {self.symbol} from {self.start_date} to {self.end_date}',
            {
                'symbol': self.symbol,
                'strategy': 'Hybrid',
                'performance': self.performance,
                'trades': self.trades,
                'equity_curve': self.equity_curve,
                'timestamp': datetime.now().isoformat()
            }
        ) 