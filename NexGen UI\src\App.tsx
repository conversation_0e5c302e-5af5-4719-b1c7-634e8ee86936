import { useState } from 'react';
import Sidebar from './components/Sidebar';
import AccountStats from './components/AccountStats';
import BacktestPanel from './components/BacktestPanel';
import BotControlPanel from './components/BotControlPanel';
import ConfigPanel from './components/ConfigPanel';
import ForwardTestPanel from './components/ForwardTestPanel';
import SystemHealth from './components/SystemHealth';
import TradeLogsTable from './components/TradeLogsTable';
import TradingChart from './components/TradingChart';
import ExplainabilityPanel from './components/ExplainabilityPanel';
import NewsFilter from './components/NewsFilter';
import AnomalyDetector from './components/AnomalyDetector';
import ChatInterface from './components/ChatInterface';

function App() {
  const [activePanel, setActivePanel] = useState('Chart');
  const [selectedTradeId, setSelectedTradeId] = useState<number | null>(null);

  const renderPanel = () => {
    switch (activePanel) {
      case 'AI Assistant':
        return <ChatInterface />;
      case 'System':
        return <SystemHealth />;
      case 'Chart':
        return <TradingChart />;
      case 'Account':
        return <AccountStats />;
      case 'Bot Control':
        return <BotControlPanel />;
      case 'Config':
        return <ConfigPanel />;
      case 'Backtest':
        return <BacktestPanel />;
      case 'Forward Test':
        return <ForwardTestPanel />;
      case 'Logs':
        return <TradeLogsTable onRowClick={(tradeId) => {
          setSelectedTradeId(tradeId);
          setActivePanel('Explainability');
        }} />;
      case 'Explainability':
        return <ExplainabilityPanel tradeId={selectedTradeId} />;
      case 'News':
        return <NewsFilter />;
      case 'Anomaly':
        return <AnomalyDetector />;
      default:
        return <TradingChart />;
    }
  };

  return (
    <div className="flex h-screen bg-terminal-bg text-terminal-text font-mono">
      <Sidebar activePanel={activePanel} setActivePanel={setActivePanel} />
        <main className="flex-1 p-8 overflow-y-auto">
        {renderPanel()}
        </main>
    </div>
  );
}

export default App;