"""
Database - PostgreSQL edition
Replaces the old SQLite backend with psycopg2 while preserving the same method
signatures so existing imports keep working.
"""

from datetime import datetime
from typing import Optional, List, Any, Dict

import psycopg2
from psycopg2.extras import <PERSON>D<PERSON><PERSON>urs<PERSON>, <PERSON><PERSON>
from types import MethodType

from app.ai_brain import TradeDecision
from app.config import config


class Database:
    def __init__(self) -> None:
        self.conn = psycopg2.connect(
            host=config.POSTGRES_HOST,
            port=config.POSTGRES_PORT,
            dbname=config.POSTGRES_DB,
            user=config.POSTGRES_USER,
            password=config.POSTGRES_PASSWORD,
        )
        self.conn.autocommit = True
        self.create_tables()

        # Wrap connection so .execute exists for legacy SQLite-style calls
        class _ConnWrapper:
            def __init__(self, _c):
                self._c = _c
            def execute(self, sql, params=None):
                cur = self._c.cursor(cursor_factory=RealDictCursor)
                if params is not None and "?" in sql:
                    # naive placeholder conversion
                    sql = sql.replace("?", "%s")
                cur.execute(sql, params or ())
                return cur
            def __getattr__(self, item):
                return getattr(self._c, item)
            def __enter__(self):
                return self._c.__enter__()
            def __exit__(self, exc_type, exc_val, exc_tb):
                return self._c.__exit__(exc_type, exc_val, exc_tb)

        self.conn = _ConnWrapper(self.conn)  # type: ignore

    # ------------------------------------------------------------------ #
    # Schema setup
    # ------------------------------------------------------------------ #
    def create_tables(self) -> None:
        """Creates all tables if they do not yet exist."""
        ddl_statements: List[str] = [
            """
            CREATE TABLE IF NOT EXISTS account_stats (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                balance  DOUBLE PRECISION NOT NULL,
                equity   DOUBLE PRECISION NOT NULL,
                free_margin DOUBLE PRECISION NOT NULL,
                margin_level DOUBLE PRECISION,
                daily_profit_loss DOUBLE PRECISION
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS trades (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                confidence DOUBLE PRECISION,
                sl_pips INTEGER,
                tp_ratio DOUBLE PRECISION,
                size DOUBLE PRECISION,
                reason TEXT,
                outcome TEXT,
                entry_price DOUBLE PRECISION,
                exit_price DOUBLE PRECISION,
                profit_loss DOUBLE PRECISION,
                duration_minutes DOUBLE PRECISION,
                order_ticket BIGINT,
                closed_at TIMESTAMP,
                status TEXT DEFAULT 'OPEN'
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS system_states (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                trading_enabled BOOLEAN NOT NULL,
                daily_drawdown_pct DOUBLE PRECISION,
                drawdown_limit_reached BOOLEAN,
                high_volatility_detected BOOLEAN,
                news_event_active BOOLEAN,
                news_details TEXT
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id SERIAL PRIMARY KEY,
                date DATE NOT NULL,
                symbol TEXT NOT NULL,
                total_trades INTEGER,
                winning_trades INTEGER,
                losing_trades INTEGER,
                win_rate DOUBLE PRECISION,
                avg_profit DOUBLE PRECISION,
                avg_loss DOUBLE PRECISION,
                profit_factor DOUBLE PRECISION,
                expectancy DOUBLE PRECISION,
                sharpe_ratio DOUBLE PRECISION,
                max_drawdown DOUBLE PRECISION
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS feature_importance (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                feature_name TEXT NOT NULL,
                importance_score DOUBLE PRECISION NOT NULL,
                model_version TEXT
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS indicator_values (
                id SERIAL PRIMARY KEY,
                trade_id INTEGER REFERENCES trades(id),
                rsi DOUBLE PRECISION,
                macd DOUBLE PRECISION,
                macd_signal DOUBLE PRECISION,
                macd_diff DOUBLE PRECISION,
                bb_upper DOUBLE PRECISION,
                bb_middle DOUBLE PRECISION,
                bb_lower DOUBLE PRECISION,
                bb_pct DOUBLE PRECISION,
                stoch_k DOUBLE PRECISION,
                stoch_d DOUBLE PRECISION,
                atr DOUBLE PRECISION
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS trade_explanations (
                id SERIAL PRIMARY KEY,
                trade_id INTEGER REFERENCES trades(id),
                explanation TEXT,
                created_at TIMESTAMP
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS calendar_events (
                id SERIAL PRIMARY KEY,
                event_id TEXT,
                time TIMESTAMP,
                epoch BIGINT,
                country TEXT,
                currency TEXT,
                importance TEXT,
                unit TEXT,
                event TEXT,
                forecast DOUBLE PRECISION,
                previous DOUBLE PRECISION,
                actual DOUBLE PRECISION,
                release_status TEXT,
                inserted_at TIMESTAMP,
                UNIQUE(event_id, time)
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS news_raw (
                id SERIAL PRIMARY KEY,
                source TEXT,
                symbol TEXT,
                json JSONB,
                inserted_at TIMESTAMP DEFAULT NOW()
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS minute_features (
                id SERIAL PRIMARY KEY,
                symbol TEXT,
                ts TIMESTAMP,
                features JSONB,
                label_vol_spike INTEGER,
                UNIQUE(symbol, ts)
            );
            """,
        ]
        with self.conn, self.conn.cursor() as cur:
            for ddl in ddl_statements:
                cur.execute(ddl)

    # ------------------------------------------------------------------ #
    # Trade-related helpers
    # ------------------------------------------------------------------ #
    def log_trade(self, decision: TradeDecision, symbol: str, result) -> int:
        """Inserts a new trade record and returns its DB id."""
        outcome_str = (
            "EXECUTED"
            if result and getattr(result, "retcode", None) == 10009
            else "FAILED"
        )

        values = (
            datetime.utcnow(),
            symbol,
            decision.action,
            decision.confidence,
            decision.sl_pips,
            decision.tp_ratio,
            getattr(result, "volume", None),
            decision.reason,
            outcome_str,
            getattr(result, "price", None),
            getattr(result, "order", None),
            "OPEN" if outcome_str == "EXECUTED" else "FAILED",
        )

        sql = """
        INSERT INTO trades (
            timestamp, symbol, action, confidence, sl_pips, tp_ratio, size,
            reason, outcome, entry_price, order_ticket, status
        )
        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING id;
        """

        with self.conn, self.conn.cursor() as cur:
            cur.execute(sql, values)
            trade_id = cur.fetchone()[0]
            print(f"[PostgreSQL] Logged trade #{trade_id} for {symbol}.")
            return trade_id

    def log_indicators(self, trade_id: int, indicators: dict) -> None:
        if not trade_id:
            print("[PostgreSQL] Cannot log indicators: invalid trade_id.")
            return

        cols = (
            "trade_id,rsi,macd,macd_signal,macd_diff,"
            "bb_upper,bb_middle,bb_lower,bb_pct,stoch_k,stoch_d,atr"
        )
        sql = f"INSERT INTO indicator_values ({cols}) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);"

        with self.conn, self.conn.cursor() as cur:
            cur.execute(
                sql,
                (
                    trade_id,
                    indicators.get("rsi"),
                    indicators.get("macd"),
                    indicators.get("macd_signal"),
                    indicators.get("macd_diff"),
                    indicators.get("bb_upper"),
                    indicators.get("bb_middle"),
                    indicators.get("bb_lower"),
                    indicators.get("bb_pct"),
                    indicators.get("stoch_k"),
                    indicators.get("stoch_d"),
                    indicators.get("atr"),
                ),
            )

    def update_trade_result(
        self, order_ticket: int, exit_price: float, profit_loss: float
    ) -> None:
        sql = """
        UPDATE trades
           SET exit_price = %s,
               profit_loss = %s,
               closed_at = NOW(),
               status = 'CLOSED',
               duration_minutes = EXTRACT(EPOCH FROM (NOW() - timestamp))/60
         WHERE order_ticket = %s;
        """
        with self.conn, self.conn.cursor() as cur:
            cur.execute(sql, (exit_price, profit_loss, order_ticket))

    # ------------------------------------------------------------------ #
    # System / performance logs
    # ------------------------------------------------------------------ #
    def log_system_state(
        self,
        trading_enabled: bool,
        daily_drawdown_pct: float,
        drawdown_limit_reached: bool,
        high_volatility: bool,
        news_active: bool,
        news_details: Optional[str] = None,
    ) -> None:
        sql = """
        INSERT INTO system_states (
            timestamp,trading_enabled,daily_drawdown_pct,
            drawdown_limit_reached,high_volatility_detected,
            news_event_active,news_details
        ) VALUES (NOW(),%s,%s,%s,%s,%s,%s);
        """
        with self.conn, self.conn.cursor() as cur:
            cur.execute(
                sql,
                (
                    trading_enabled,
                    daily_drawdown_pct,
                    drawdown_limit_reached,
                    high_volatility,
                    news_active,
                    news_details,
                ),
            )

    # ------------------------------------------------------------------ #
    # Calendar events
    # ------------------------------------------------------------------ #
    def log_calendar_events(self, events: List[Dict[str, Any]]) -> None:
        if not events:
            return
        rows = [
            (
                str(ev.get("event_id") or ev.get("id")),
                ev.get("time"),
                ev.get("epoch"),
                ev.get("country"),
                ev.get("impact"),
                ev.get("unit"),
                ev.get("event"),
                ev.get("forecast"),
                ev.get("previous"),
                ev.get("actual"),
                ev.get("release_status"),
                datetime.utcnow(),
            )
            for ev in events
        ]

        sql = """
        INSERT INTO calendar_events (
            event_id,time,epoch,country,importance,unit,event,
            forecast,previous,actual,release_status,inserted_at
        )
        VALUES (
            %s,%s,%s,%s,%s,%s,%s,
            %s,%s,%s,%s,%s
        )
        ON CONFLICT (event_id, time) DO NOTHING;
        """

        with self.conn, self.conn.cursor() as cur:
            cur.executemany(sql, rows)

    def log_news_raw(self, source, symbol, raw_json):
        with self.conn, self.conn.cursor() as cur:
            cur.execute(
                "INSERT INTO news_raw (source, symbol, json) VALUES (%s, %s, %s)",
                (source, symbol, Json(raw_json))
            )

    def log_minute_features(self, symbol, ts, features, label_vol_spike=None):
        with self.conn, self.conn.cursor() as cur:
            cur.execute(
                "INSERT INTO minute_features (symbol, ts, features, label_vol_spike) VALUES (%s, %s, %s, %s) ON CONFLICT DO NOTHING",
                (symbol, ts, Json(features), label_vol_spike)
            )

    def get_trade_logs(self, limit: int = 100):
        """Return latest trade logs for frontend."""
        sql = """
        SELECT id, timestamp, symbol, action AS type, size, entry_price AS price,
               COALESCE(profit_loss,0) AS profit_loss, outcome
          FROM trades
         ORDER BY id DESC
         LIMIT %s;
        """
        with self.conn, self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(sql, (limit,))
            rows = cur.fetchall()
            # Convert Decimal/None to float-friendly types
            clean = []
            for r in rows:
                r = dict(r)
                if r.get("price") is not None:
                    r["price"] = float(r["price"])
                if r.get("profit_loss") is not None:
                    r["profit_loss"] = float(r["profit_loss"])
                clean.append(r)
            return clean

    def get_explanation_for_trade(self, trade_id: int):
        """Fetch cached explanation for given trade id, or None."""
        sql = "SELECT * FROM trade_explanations WHERE trade_id=%s ORDER BY id DESC LIMIT 1;"
        with self.conn, self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(sql, (trade_id,))
            row = cur.fetchone()
            return dict(row) if row else None


# Single shared instance
db = Database()