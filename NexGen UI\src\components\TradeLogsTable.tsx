import React, { useState, useEffect } from 'react';
import { api, TradeLog } from '../services/api';
import { Table, ArrowUp, ArrowDown, Loader2, ServerCrash, Eye } from 'lucide-react';

interface TradeLogsTableProps {
  onRowClick: (tradeId: number) => void;
}

const TradeLogsTable: React.FC<TradeLogsTableProps> = ({ onRowClick }) => {
  const [logs, setLogs] = useState<TradeLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setIsLoading(true);
        const response = await api.getTradeLogs(100);
        setLogs(response.logs);
        setError(null);
      } catch (err) {
        setError("Failed to fetch trade logs.");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchLogs();
  }, []);

  if (isLoading) return (
    <div className="terminal-card p-6 flex items-center justify-center h-full">
        <Loader2 className="animate-spin text-neon-blue" size={48} />
    </div>
  );

  if (error) return (
    <div className="terminal-card p-6 flex flex-col items-center justify-center h-full bg-neon-red/10 border border-neon-red">
        <ServerCrash className="text-neon-red" size={48} />
        <p className="mt-4 text-lg font-mono text-neon-red">{error}</p>
    </div>
  );

  return (
    <div className="terminal-card p-4 h-full flex flex-col">
      <div className="flex-shrink-0 flex items-center space-x-3 mb-4">
        <div className="p-2 bg-neon-blue/20 rounded-lg">
            <Table className="text-neon-blue" size={24} />
        </div>
        <h2 className="text-xl font-bold text-terminal-text">Trade Logs</h2>
      </div>
      <div className="overflow-y-auto flex-grow">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-terminal-muted uppercase sticky top-0 bg-terminal-card z-10">
            <tr>
              <th className="p-4">ID</th>
              <th className="p-4">Timestamp</th>
              <th className="p-4">Symbol</th>
              <th className="p-4">Type</th>
              <th className="p-4">Size</th>
              <th className="p-4">Price</th>
              <th className="p-4">P/L</th>
              <th className="p-4">Outcome</th>
            </tr>
          </thead>
          <tbody className="text-terminal-text">
          {logs.map((log) => (
            <tr 
              key={log.id} 
              className="border-b border-terminal-border hover:bg-terminal-card/50 transition-colors duration-200 cursor-pointer"
              onClick={() => onRowClick(log.id)}
            >
              <td className="p-4">
                <div className="flex items-center space-x-2">
                  <Eye className="text-neon-blue" size={16} />
                  <span>{log.id}</span>
                </div>
              </td>
              <td className="p-4">{new Date(log.timestamp).toLocaleString()}</td>
              <td className="p-4 font-bold">{log.symbol}</td>
              <td className={`p-4 font-bold ${log.type === 'BUY' ? 'text-neon-green' : 'text-neon-red'}`}>
                <div className="flex items-center space-x-1">
                    {log.type === 'BUY' ? <ArrowUp size={14} /> : <ArrowDown size={14} />}
                    <span>{log.type}</span>
                </div>
              </td>
              <td className="p-4">{log.size || 'N/A'}</td>
              <td className="p-4">{log.price ? log.price.toFixed(5) : 'N/A'}</td>
              <td className={`p-4 font-medium ${log.profit_loss >= 0 ? 'text-neon-green' : 'text-neon-red'}`}>
                {log.profit_loss ? log.profit_loss.toFixed(2) : '0.00'}
              </td>
              <td className="p-4">
                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    log.outcome === 'WIN' ? 'bg-neon-green/20 text-neon-green' :
                    log.outcome === 'LOSS' ? 'bg-neon-red/20 text-neon-red' :
                    log.outcome === 'EXECUTED' ? 'bg-neon-blue/20 text-neon-blue' :
                    log.outcome === 'FAILED' ? 'bg-neon-red/20 text-neon-red' :
                    'bg-terminal-bg border border-terminal-border'
                }`}>
                    {log.outcome}
                </span>
              </td>
            </tr>
          ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TradeLogsTable;