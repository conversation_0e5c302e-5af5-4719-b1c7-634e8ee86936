import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, Send, Bot, User, Terminal, Zap, Mic, MicOff, Volume2, VolumeX, Maximize2, Minimize2, X, Sidebar } from 'lucide-react';
import api from '../services/api';
import { useSpeechRecognition } from '../utils/useSpeechRecognition';
import { ChatSessionSidebar } from './ChatSessionSidebar';
import { chatStorage, ChatMessage, ChatSession } from '../utils/chatSessionStorage';

// Helper function to format messages with basic markdown-like styling
const formatMessage = (message: string) => {
  const lines = message.split('\n');

  return lines.map((line, index) => {
    // Handle headers (lines starting with **)
    if (line.match(/^\*\*.*\*\*$/)) {
      const text = line.replace(/\*\*/g, '');
      return (
        <div key={index} className="font-bold text-neon-blue mb-2 text-base">
          {text}
        </div>
      );
    }

    // Handle bold text (**text**)
    if (line.includes('**')) {
      const parts = line.split(/(\*\*.*?\*\*)/);
      return (
        <div key={index} className="mb-1">
          {parts.map((part, partIndex) => {
            if (part.startsWith('**') && part.endsWith('**')) {
              return (
                <span key={partIndex} className="font-bold text-neon-green">
                  {part.replace(/\*\*/g, '')}
                </span>
              );
            }
            return <span key={partIndex}>{part}</span>;
          })}
        </div>
      );
    }

    // Handle bullet points (lines starting with •)
    if (line.startsWith('•')) {
      return (
        <div key={index} className="ml-4 mb-1 flex items-start">
          <span className="text-neon-purple mr-2 mt-0.5">•</span>
          <span>{line.substring(1).trim()}</span>
        </div>
      );
    }

    // Handle status indicators (🟢, 🔴, etc.)
    if (line.match(/^[🟢🔴🟡⚠️✅❌📊💰📈🔍🤖📅🚀⚙️📰]/)) {
      return (
        <div key={index} className="mb-1 font-medium">
          {line}
        </div>
      );
    }

    // Regular lines
    if (line.trim()) {
      return (
        <div key={index} className="mb-1">
          {line}
        </div>
      );
    }

    // Empty lines for spacing
    return <div key={index} className="mb-2"></div>;
  });
};

const ChatInterface: React.FC = () => {
  // Session management state
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Chat state
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [useMultiAgent, setUseMultiAgent] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);

  // Typing effect state
  const [typingMessageId, setTypingMessageId] = useState<string | null>(null);
  const [displayedText, setDisplayedText] = useState<string>('');
  const typingIntervalRef = useRef<number | null>(null);

  // Initialize session on component mount
  useEffect(() => {
    const session = chatStorage.getCurrentSession();
    setCurrentSession(session);
    setMessages(session.messages);
    
    // Add welcome message if this is a new session with no messages
    if (session.messages.length === 0) {
      const welcomeText = '👋 **Hello! I\'m your NexGen TraderPro AI Assistant**\n\nI\'m here to help with anything you need - from trading analysis to casual conversation! I can:\n\n• 💬 **Chat naturally** about any topic\n• 📊 **Analyze your trades** and market conditions\n• 💰 **Check account status** and performance\n• 🤖 **Control bot settings** and operations\n• 🧠 **Explain trading decisions** with AI insights\n\nJust start typing and I\'ll suggest relevant questions, or ask me anything that comes to mind!';

      const welcomeMessage: ChatMessage = {
        id: '1',
        type: 'bot',
        message: '', // Start empty for typing effect
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages([welcomeMessage]);

      // Start typing effect for welcome message
      setTimeout(() => {
        typeMessage(welcomeMessage.id, welcomeText, () => {
          const completedWelcome = { ...welcomeMessage, message: welcomeText };
          chatStorage.addMessage(session.id, completedWelcome);
          setMessages([completedWelcome]);
        });
      }, 500); // Small delay before starting to type
    }
  }, []);

  // Speech recognition hook
  const {
    isListening,
    transcript,
    interimTranscript,
    error: speechError,
    startListening,
    stopListening,
    resetTranscript,
    visualizerRef,
    isSupported: isSpeechSupported
  } = useSpeechRecognition({
    continuous: true,
    interimResults: true,
    visualize: true,
    visualizerOptions: {
      lineColor: '#00CCFF', 
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
      lineWidth: 3
    }
  });

  // Update input message when transcript changes
  useEffect(() => {
    if (isListening && !isVoiceMode) {
      setInputMessage(transcript);
    }
  }, [transcript, isListening, isVoiceMode]);

  // In voice mode, automatically send message when we get a final transcript
  useEffect(() => {
    if (isVoiceMode && transcript && !isTyping && !isSpeaking) {
      const timer = setTimeout(() => {
        if (transcript.trim()) {
          handleSendMessage(transcript);
          resetTranscript();
        }
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [transcript, isVoiceMode, isTyping, isSpeaking]);

  // Auto restart listening in voice mode after speaking finishes
  useEffect(() => {
    if (isVoiceMode && !isListening && !isSpeaking) {
      const timer = setTimeout(() => {
        startListening();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [isVoiceMode, isListening, isSpeaking]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, displayedText]);

  // Cleanup typing interval on unmount
  useEffect(() => {
    return () => {
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
      }
    };
  }, []);

  // Typing effect function with realistic speed variation
  const typeMessage = (messageId: string, fullText: string, onComplete?: () => void) => {
    setTypingMessageId(messageId);
    setDisplayedText('');

    let currentIndex = 0;

    if (typingIntervalRef.current) {
      clearInterval(typingIntervalRef.current);
    }

    const typeNextCharacter = () => {
      if (currentIndex < fullText.length) {
        setDisplayedText(fullText.substring(0, currentIndex + 1));
        currentIndex++;

        // Variable typing speed for more realistic effect
        let delay = 15; // Base speed (faster)
        const char = fullText[currentIndex - 1];

        // Slower after punctuation
        if (char === '.' || char === '!' || char === '?') {
          delay = 80;
        } else if (char === ',' || char === ';' || char === ':') {
          delay = 50;
        } else if (char === ' ') {
          delay = 25;
        } else if (char === '\n') {
          delay = 100;
        }

        // Add small random variation
        delay += Math.random() * 10;

        setTimeout(typeNextCharacter, delay);
      } else {
        setTypingMessageId(null);
        setDisplayedText('');
        if (onComplete) {
          onComplete();
        }
      }
    };

    // Start typing after a brief pause
    setTimeout(typeNextCharacter, 100);
  };

  // Smart suggestions for different contexts
  const suggestions = [
    "Hi, how are you?",
    "What can you help me with?",
    "Tell me about yourself",
    "What's my account balance?",
    "Show me my open positions",
    "How is the bot performing today?",
    "What's the current bot status?",
    "Check my live account balance",
    "What's the market outlook for EURUSD?",
    "Should I be worried about my current trades?",
    "What currency pairs are you monitoring?",
    "Give me a market analysis",
    "What's your risk assessment?",
    "How much profit have I made this week?",
    "Show me my recent trading history",
    "What's my win rate?",
    "Analyze my trading performance",
    "Run a new market analysis",
    "Generate fresh trading signals",
    "Explain your last trade decision",
    "Update my risk settings",
    "What do you think about the current market?",
    "How does your AI trading system work?",
    "What makes you different from other trading bots?",
    "Can you explain technical analysis to me?"
  ];

  // Enhanced quick commands
  const quickCommands = [
    "🤖 Bot status",
    "📊 Open trades", 
    "💰 Account info",
    "📈 Performance summary",
    "🔍 Market analysis",
    "⚠️ Risk assessment",
    "📅 Weekly summary",
    "🚀 Run reasoning cycle"
  ];

  // Handle input changes and filter suggestions
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputMessage(value);

    if (value.trim().length > 0) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(value.toLowerCase()) ||
        value.toLowerCase().split(' ').some(word =>
          suggestion.toLowerCase().includes(word) && word.length > 2
        )
      ).slice(0, 6);

      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0 && value.length > 1);
      setSelectedSuggestionIndex(-1);
    } else {
      setShowSuggestions(false);
      setFilteredSuggestions([]);
    }
  };

  // Handle keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev =>
        prev < filteredSuggestions.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev =>
        prev > 0 ? prev - 1 : filteredSuggestions.length - 1
      );
    } else if (e.key === 'Tab' && selectedSuggestionIndex >= 0) {
      e.preventDefault();
      setInputMessage(filteredSuggestions[selectedSuggestionIndex]);
      setShowSuggestions(false);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
    }
  };

  // Handle suggestion selection
  const selectSuggestion = (suggestion: string) => {
    setInputMessage(suggestion);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    inputRef.current?.focus();
  };

  const handleSendMessage = async (message?: string) => {
    const messageToSend = message || inputMessage;
    if (!messageToSend.trim() || !currentSession) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      message: messageToSend,
      timestamp: new Date().toLocaleTimeString()
    };

    // Add user message to session storage and local state
    chatStorage.addMessage(currentSession.id, userMessage);
    setMessages(prev => [...prev, userMessage]);
    if (!message) setInputMessage('');
    setIsTyping(true);
    setIsLoading(true);

    try {
      // Call our API
      const response = await api.chat({
        message: messageToSend,
        use_multi_agent: useMultiAgent
      });

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        message: '', // Start with empty message for typing effect
        timestamp: new Date().toLocaleTimeString()
      };

      // Add empty bot message to state first
      setMessages(prev => [...prev, botMessage]);

      // Start typing effect
      typeMessage(botMessage.id, response.response, () => {
        // Once typing is complete, update the message in storage and state
        const completedMessage = { ...botMessage, message: response.response };
        chatStorage.addMessage(currentSession.id, completedMessage);
        setMessages(prev =>
          prev.map(msg =>
            msg.id === botMessage.id
              ? completedMessage
              : msg
          )
        );

        // Text-to-speech after typing is complete
        if ((isSpeechEnabled || isVoiceMode) && 'speechSynthesis' in window) {
          if (isListening) {
            stopListening();
          }

          setIsSpeaking(true);

          const utterance = new SpeechSynthesisUtterance(response.response);
          utterance.rate = 0.9;
          utterance.pitch = 1;
          utterance.onend = () => {
            setIsSpeaking(false);
            if (isVoiceMode) {
              setTimeout(() => startListening(), 500);
            }
          };
          speechSynthesis.speak(utterance);
        }
      });
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        message: 'Sorry, I encountered an error processing your request. Please try again later.',
        timestamp: new Date().toLocaleTimeString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (showSuggestions && selectedSuggestionIndex >= 0) {
        selectSuggestion(filteredSuggestions[selectedSuggestionIndex]);
      } else {
        handleSendMessage();
        setShowSuggestions(false);
      }
    }
  };

  // Session management functions
  const handleNewSession = () => {
    const newSession = chatStorage.createSession();
    setCurrentSession(newSession);
    setMessages([]);

    // Add welcome message to new session with typing effect
    const welcomeText = '👋 **Hello! I\'m your NexGen TraderPro AI Assistant**\n\nI\'m here to help with anything you need - from trading analysis to casual conversation! I can:\n\n• 💬 **Chat naturally** about any topic\n• 📊 **Analyze your trades** and market conditions\n• 💰 **Check account status** and performance\n• 🤖 **Control bot settings** and operations\n• 🧠 **Explain trading decisions** with AI insights\n\nJust start typing and I\'ll suggest relevant questions, or ask me anything that comes to mind!';

    const welcomeMessage: ChatMessage = {
      id: '1',
      type: 'bot',
      message: '', // Start empty for typing effect
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages([welcomeMessage]);

    // Start typing effect for welcome message
    setTimeout(() => {
      typeMessage(welcomeMessage.id, welcomeText, () => {
        const completedWelcome = { ...welcomeMessage, message: welcomeText };
        chatStorage.addMessage(newSession.id, completedWelcome);
        setMessages([completedWelcome]);
      });
    }, 500);
  };

  const handleSessionSelect = (sessionId: string) => {
    const session = chatStorage.getSession(sessionId);
    if (session) {
      chatStorage.setCurrentSessionId(sessionId);
      setCurrentSession(session);
      setMessages(session.messages);
    }
  };

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const toggleVoiceMode = () => {
    if (isVoiceMode) {
      setIsVoiceMode(false);
      stopListening();
      if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
      }
      setIsSpeaking(false);
    } else {
      setIsVoiceMode(true);
      setIsExpanded(false);
      startListening();
    }
  };

  // Enhanced quick command handler
  const handleQuickCommand = (command: string) => {
    const cleanCommand = command.replace(/^[^\w\s]+\s*/, '').trim();
    handleSendMessage(cleanCommand);
  };

  // Voice Mode Interface (Full Screen)
  if (isVoiceMode) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-terminal-bg via-terminal-surface to-terminal-card z-50 flex flex-col items-center justify-center">
        {/* Header */}
        <div className="absolute top-8 left-8 right-8 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-neon-purple/20 rounded-lg">
              <MessageCircle className="text-neon-purple" size={24} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-terminal-text">Voice Mode</h2>
              <p className="text-sm text-terminal-muted font-mono">Trading AI Assistant</p>
            </div>
          </div>

          <button
            onClick={toggleVoiceMode}
            className="p-3 rounded-full bg-terminal-surface/80 backdrop-blur-sm text-terminal-muted hover:text-terminal-text border border-terminal-border transition-all duration-200"
          >
            <X size={24} />
          </button>
        </div>

        {/* Central Orb */}
        <div className="flex flex-col items-center space-y-8">
          <div className="relative">
            <div className={`w-48 h-48 rounded-full flex items-center justify-center transition-all duration-500 ${
              isListening
                ? 'bg-gradient-to-br from-neon-blue/30 via-neon-purple/30 to-neon-green/30 animate-pulse shadow-glow-blue'
                : isSpeaking
                  ? 'bg-gradient-to-br from-neon-green/30 via-neon-blue/30 to-neon-purple/30 animate-pulse shadow-glow-green'
                  : 'bg-gradient-to-br from-terminal-surface to-terminal-card border-2 border-terminal-border'
            }`}>
              <canvas
                ref={visualizerRef}
                className="absolute inset-0 w-full h-full rounded-full"
                style={{ opacity: isListening || isSpeaking ? 1 : 0.3 }}
              />

              <div className="absolute inset-0 flex items-center justify-center z-10">
                {isListening ? (
                  <Mic className="text-white" size={48} />
                ) : isSpeaking ? (
                  <Volume2 className="text-white" size={48} />
                ) : (
                  <Bot className="text-terminal-muted" size={48} />
                )}
              </div>
            </div>
          </div>

          {/* Status Text */}
          <div className="text-center space-y-2 max-w-md">
            <div className="text-2xl font-bold text-terminal-text font-mono">
              {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : isTyping ? 'Thinking...' : 'Ready to chat'}
            </div>
            <div className="text-terminal-muted font-mono">
              {isListening ? 'Speak now, I\'m listening' : isSpeaking ? 'AI is responding' : 'Tap the orb or say something'}
            </div>

            {isListening && interimTranscript && (
              <div className="mt-4 p-3 bg-terminal-bg/60 rounded-lg border border-terminal-border text-terminal-text font-mono">
                {interimTranscript}
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-6">
          <button
            onClick={isListening ? stopListening : startListening}
            disabled={isSpeaking}
            className={`p-4 rounded-full transition-all duration-200 ${
              isListening
                ? 'bg-neon-red/20 text-neon-red border-2 border-neon-red/50'
                : 'bg-terminal-surface/80 backdrop-blur-sm text-terminal-muted hover:text-terminal-text border-2 border-terminal-border'
            } disabled:opacity-50`}
          >
            {isListening ? <MicOff size={24} /> : <Mic size={24} />}
          </button>
        </div>
      </div>
    );
  }

  // Regular Chat Interface
  return (
    <div className={`flex h-full w-full transition-all duration-300 ${isExpanded ? 'fixed inset-4 z-50' : ''}`}>
      {/* Chat Session Sidebar */}
      {showSidebar && (
        <ChatSessionSidebar
          currentSessionId={currentSession?.id || null}
          onSessionSelect={handleSessionSelect}
          onNewSession={handleNewSession}
          isCollapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      )}

      {/* Main Chat Interface */}
      <div className={`terminal-card p-6 flex flex-col max-h-[80vh] w-full transition-all duration-300 ${showSidebar ? 'ml-0' : ''}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg bg-terminal-surface text-terminal-muted border border-terminal-border hover:text-terminal-text transition-colors"
              title="Toggle Chat History"
            >
              <Sidebar size={16} />
            </button>
            <div className="p-2 bg-neon-purple/20 rounded-lg">
              <MessageCircle className="text-neon-purple" size={24} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-terminal-text">AI Assistant</h2>
              <p className="text-sm text-terminal-muted font-mono">
                {currentSession?.title || 'Professional Trading Co-Pilot'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleNewSession}
              className="p-2 rounded-lg bg-neon-blue/20 text-neon-blue border border-neon-blue/30 hover:bg-neon-blue/30 transition-all duration-200"
              title="New Chat"
            >
              <MessageCircle size={16} />
            </button>

            <button
              onClick={toggleVoiceMode}
              className="p-2 rounded-lg bg-gradient-to-r from-neon-purple to-neon-blue hover:from-neon-blue hover:to-neon-purple text-white transition-all duration-200 shadow-glow-purple"
              title="Voice Mode"
            >
              <Mic size={16} />
            </button>

            <button
              onClick={() => setIsSpeechEnabled(!isSpeechEnabled)}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isSpeechEnabled
                  ? 'bg-neon-green/20 text-neon-green border border-neon-green/30'
                  : 'bg-terminal-surface text-terminal-muted border border-terminal-border hover:text-terminal-text'
              }`}
              title="Toggle Text-to-Speech"
            >
              {isSpeechEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />}
            </button>

            <label className="flex items-center space-x-1 cursor-pointer select-none text-xs font-mono">
              <input
                type="checkbox"
                checked={useMultiAgent}
                onChange={e => setUseMultiAgent(e.target.checked)}
                className="accent-neon-yellow"
              />
              <Zap className={useMultiAgent ? "text-neon-yellow" : "text-terminal-muted"} size={16} />
              <span>Multi-Agent</span>
            </label>

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-lg bg-terminal-surface text-terminal-muted border border-terminal-border hover:text-terminal-text transition-colors"
              title={isExpanded ? "Minimize" : "Expand"}
            >
              {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            </button>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 min-h-0 overflow-y-auto mb-4">
          <div className="space-y-4">
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`flex items-start space-x-3 max-w-[85%] ${msg.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div className={`p-2 rounded-lg flex-shrink-0 ${
                    msg.type === 'user'
                      ? 'bg-neon-blue/20 border border-neon-blue/30'
                      : 'bg-neon-green/20 border border-neon-green/30'
                  }`}>
                    {msg.type === 'user' ? (
                      <User className="text-neon-blue" size={16} />
                    ) : (
                      <Bot className="text-neon-green" size={16} />
                    )}
                  </div>
                  <div className={`rounded-xl p-4 ${
                    msg.type === 'user'
                      ? 'bg-neon-blue/10 border border-neon-blue/20'
                      : 'bg-terminal-bg border border-terminal-border'
                  }`}>
                    <div className="text-terminal-text font-mono text-sm leading-relaxed break-words">
                      {msg.type === 'bot' && typingMessageId === msg.id ? (
                        <>
                          {formatMessage(displayedText)}
                          <span className="inline-block w-2 h-4 bg-neon-green ml-1 animate-pulse">|</span>
                        </>
                      ) : (
                        formatMessage(msg.message)
                      )}
                    </div>
                    <span className="text-xs text-terminal-muted font-mono mt-2 block">{msg.timestamp}</span>
                  </div>
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="flex items-start space-x-3">
                  <div className="p-2 rounded-lg bg-neon-green/20 border border-neon-green/30">
                    <Bot className="text-neon-green" size={16} />
                  </div>
                  <div className="bg-terminal-bg border border-terminal-border rounded-xl p-4">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Commands */}
        {!inputMessage.trim() && (
          <div className="mb-4">
            <div className="mb-2">
              <span className="text-xs text-terminal-muted font-mono">Quick Actions:</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {quickCommands.slice(0, 4).map((command, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickCommand(command)}
                  className="text-xs bg-gradient-to-r from-terminal-surface to-terminal-card hover:from-terminal-card hover:to-terminal-surface text-terminal-muted hover:text-terminal-text px-3 py-2.5 rounded-lg border border-terminal-border transition-all duration-200 font-mono hover:border-neon-purple/30 hover:shadow-glow-purple/20 text-left"
                >
                  {command}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Terminal className="absolute left-3 top-1/2 transform -translate-y-1/2 text-terminal-muted" size={16} />
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                handleKeyDown(e);
                if (e.key === 'Enter' && !e.shiftKey) {
                  handleKeyPress(e);
                }
              }}
              onFocus={() => {
                if (inputMessage.trim().length > 1) {
                  setShowSuggestions(filteredSuggestions.length > 0);
                }
              }}
              onBlur={() => {
                setTimeout(() => setShowSuggestions(false), 150);
              }}
              placeholder="Ask me anything - trading questions, general chat, or commands..."
              className="w-full pl-10 pr-16 py-3 bg-terminal-bg border border-terminal-border rounded-lg text-terminal-text font-mono focus:outline-none focus:ring-2 focus:ring-neon-purple focus:border-transparent transition-all duration-200 resize-none"
              disabled={isListening}
            />

            {/* Suggestions Popup */}
            {showSuggestions && filteredSuggestions.length > 0 && (
              <div className="absolute bottom-full left-0 right-0 mb-2 bg-terminal-surface border border-terminal-border rounded-lg shadow-2xl z-50 max-h-48 overflow-y-auto">
                {filteredSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => selectSuggestion(suggestion)}
                    className={`w-full text-left px-4 py-3 text-sm font-mono transition-all duration-150 border-b border-terminal-border/50 last:border-b-0 ${
                      index === selectedSuggestionIndex
                        ? 'bg-neon-purple/20 text-neon-purple border-neon-purple/30'
                        : 'text-terminal-text hover:bg-terminal-card hover:text-neon-blue'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-terminal-muted">💬</span>
                      <span>{suggestion}</span>
                    </div>
                  </button>
                ))}
                <div className="px-4 py-2 text-xs text-terminal-muted border-t border-terminal-border/50 bg-terminal-bg/50">
                  <span>↑↓ Navigate • Tab/Enter to select • Esc to close</span>
                </div>
              </div>
            )}

            {/* Voice Input Button */}
            <button
              onClick={isListening ? stopListening : startListening}
              disabled={isTyping || !isSpeechSupported}
              className={`absolute right-12 top-1/2 transform -translate-y-1/2 p-1.5 rounded-lg transition-all duration-200 ${
                isListening
                  ? 'bg-neon-red/20 text-neon-red animate-pulse'
                  : 'bg-terminal-surface text-terminal-muted hover:text-terminal-text hover:bg-terminal-card'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
              title={isSpeechSupported ? "Voice Input" : "Speech Recognition Not Supported"}
            >
              {isListening ? <MicOff size={14} /> : <Mic size={14} />}
            </button>

            <button
              onClick={() => handleSendMessage()}
              disabled={!inputMessage.trim() || isTyping || isLoading}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-lg bg-terminal-surface text-terminal-muted hover:text-neon-blue hover:bg-terminal-card transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="w-3.5 h-3.5 border border-terminal-muted border-t-neon-blue rounded-full animate-spin"></div>
              ) : (
                <Send size={14} />
              )}
            </button>
          </div>
        </div>

        {/* Speech Recognition Error Display */}
        {speechError && (
          <div className="mt-2 text-xs text-neon-red font-mono">
            {speechError.message}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
