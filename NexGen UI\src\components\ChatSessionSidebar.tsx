import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Plus, 
  Trash2, 
  Edit3, 
  Check, 
  X,
  MoreVertical,
  Download,
  Upload
} from 'lucide-react';
import { ChatSession, chatStorage } from '../utils/chatSessionStorage';

interface ChatSessionSidebarProps {
  currentSessionId: string | null;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const ChatSessionSidebar: React.FC<ChatSessionSidebarProps> = ({
  currentSessionId,
  onSessionSelect,
  onNewSession,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showMenu, setShowMenu] = useState<string | null>(null);

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = () => {
    const allSessions = chatStorage.getAllSessions();
    setSessions(allSessions);
  };

  const handleNewSession = () => {
    onNewSession();
    loadSessions();
  };

  const handleSessionSelect = (sessionId: string) => {
    onSessionSelect(sessionId);
    setShowMenu(null);
  };

  const handleDeleteSession = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this chat session?')) {
      chatStorage.deleteSession(sessionId);
      loadSessions();
      
      // If we deleted the current session, the storage will auto-switch
      if (sessionId === currentSessionId) {
        const remainingSessions = chatStorage.getAllSessions();
        if (remainingSessions.length > 0) {
          onSessionSelect(remainingSessions[0].id);
        } else {
          onNewSession();
        }
      }
    }
    setShowMenu(null);
  };

  const handleEditTitle = (sessionId: string, currentTitle: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingSessionId(sessionId);
    setEditingTitle(currentTitle);
    setShowMenu(null);
  };

  const handleSaveTitle = () => {
    if (editingSessionId && editingTitle.trim()) {
      chatStorage.updateSessionTitle(editingSessionId, editingTitle.trim());
      loadSessions();
    }
    setEditingSessionId(null);
    setEditingTitle('');
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditingTitle('');
  };

  const handleExportSessions = () => {
    const data = chatStorage.exportSessions();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nexgen-chat-sessions-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setShowMenu(null);
  };

  const handleImportSessions = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          if (chatStorage.importSessions(data)) {
            loadSessions();
            alert('Sessions imported successfully!');
          } else {
            alert('Failed to import sessions. Please check the file format.');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
    setShowMenu(null);
  };

  if (isCollapsed) {
    return (
      <div className="w-12 bg-terminal-surface border-r border-terminal-border flex flex-col items-center py-4 space-y-3">
        <button
          onClick={handleNewSession}
          className="p-2 rounded-lg bg-neon-blue/20 text-neon-blue hover:bg-neon-blue/30 transition-colors"
          title="New Chat"
        >
          <Plus size={16} />
        </button>
        
        <div className="flex-1 overflow-y-auto space-y-2">
          {sessions.slice(0, 5).map((session) => (
            <button
              key={session.id}
              onClick={() => handleSessionSelect(session.id)}
              className={`p-2 rounded-lg transition-colors ${
                session.id === currentSessionId
                  ? 'bg-neon-purple/30 text-neon-purple'
                  : 'text-terminal-muted hover:text-terminal-text hover:bg-terminal-card'
              }`}
              title={session.title}
            >
              <MessageSquare size={16} />
            </button>
          ))}
        </div>
        
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className="p-2 rounded-lg text-terminal-muted hover:text-terminal-text hover:bg-terminal-card transition-colors"
            title="Expand Sidebar"
          >
            <MoreVertical size={16} />
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="w-80 bg-terminal-surface border-r border-terminal-border flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-terminal-border">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-terminal-text">Chat Sessions</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowMenu(showMenu === 'main' ? null : 'main')}
              className="p-1 rounded text-terminal-muted hover:text-terminal-text hover:bg-terminal-card transition-colors"
              title="Menu"
            >
              <MoreVertical size={16} />
            </button>
            {onToggleCollapse && (
              <button
                onClick={onToggleCollapse}
                className="p-1 rounded text-terminal-muted hover:text-terminal-text hover:bg-terminal-card transition-colors"
                title="Collapse Sidebar"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </div>
        
        {/* Menu Dropdown */}
        {showMenu === 'main' && (
          <div className="absolute z-10 mt-2 w-48 bg-terminal-card border border-terminal-border rounded-lg shadow-lg">
            <button
              onClick={handleExportSessions}
              className="w-full px-3 py-2 text-left text-terminal-text hover:bg-terminal-surface transition-colors flex items-center space-x-2"
            >
              <Download size={14} />
              <span>Export Sessions</span>
            </button>
            <button
              onClick={handleImportSessions}
              className="w-full px-3 py-2 text-left text-terminal-text hover:bg-terminal-surface transition-colors flex items-center space-x-2"
            >
              <Upload size={14} />
              <span>Import Sessions</span>
            </button>
          </div>
        )}
        
        <button
          onClick={handleNewSession}
          className="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-neon-blue to-neon-purple hover:from-neon-purple hover:to-neon-blue text-white rounded-lg transition-all duration-200 shadow-glow-blue"
        >
          <Plus size={16} />
          <span>New Chat</span>
        </button>
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto p-2">
        {sessions.length === 0 ? (
          <div className="text-center text-terminal-muted py-8">
            <MessageSquare size={32} className="mx-auto mb-2 opacity-50" />
            <p>No chat sessions yet</p>
            <p className="text-sm">Start a new conversation!</p>
          </div>
        ) : (
          <div className="space-y-1">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                  session.id === currentSessionId
                    ? 'bg-neon-purple/20 border border-neon-purple/30'
                    : 'hover:bg-terminal-card border border-transparent'
                }`}
                onClick={() => handleSessionSelect(session.id)}
              >
                {editingSessionId === session.id ? (
                  <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="flex-1 bg-terminal-bg border border-terminal-border rounded px-2 py-1 text-sm text-terminal-text focus:outline-none focus:border-neon-blue"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveTitle();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                    />
                    <button
                      onClick={handleSaveTitle}
                      className="p-1 text-neon-green hover:bg-neon-green/20 rounded"
                    >
                      <Check size={12} />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="p-1 text-neon-red hover:bg-neon-red/20 rounded"
                    >
                      <X size={12} />
                    </button>
                  </div>
                ) : (
                  <>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-terminal-text truncate">
                          {session.title}
                        </h4>
                        <p className="text-xs text-terminal-muted mt-1">
                          {session.messages.length} messages • {new Date(session.updatedAt).toLocaleDateString()}
                        </p>
                      </div>
                      
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowMenu(showMenu === session.id ? null : session.id);
                          }}
                          className="p-1 text-terminal-muted hover:text-terminal-text hover:bg-terminal-surface rounded"
                        >
                          <MoreVertical size={12} />
                        </button>
                      </div>
                    </div>
                    
                    {/* Session Menu */}
                    {showMenu === session.id && (
                      <div className="absolute right-2 top-8 z-10 w-32 bg-terminal-card border border-terminal-border rounded-lg shadow-lg">
                        <button
                          onClick={(e) => handleEditTitle(session.id, session.title, e)}
                          className="w-full px-3 py-2 text-left text-terminal-text hover:bg-terminal-surface transition-colors flex items-center space-x-2"
                        >
                          <Edit3 size={12} />
                          <span>Rename</span>
                        </button>
                        <button
                          onClick={(e) => handleDeleteSession(session.id, e)}
                          className="w-full px-3 py-2 text-left text-neon-red hover:bg-neon-red/20 transition-colors flex items-center space-x-2"
                        >
                          <Trash2 size={12} />
                          <span>Delete</span>
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
