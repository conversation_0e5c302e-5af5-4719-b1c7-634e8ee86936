import MetaTrader5 as mt5
import time
from app.config import config
from app.ai_brain import TradeDecision
from app.mt5_manager import mt5_manager

class TradeExecutor:
    """
    Handles trade execution, position sizing, and risk management.
    """
    def __init__(self, account_type='demo'):
        self.account_type = account_type
        self.slippage = 5 # Max allowed deviation in points

    def calculate_position_size(self, symbol: str, sl_pips: float) -> float:
        """
        Calculates position size based on risk percentage and stop loss.
        """
        # Ensure we're connected to the correct account
        if not mt5_manager.ensure_connection(self.account_type):
            print(f"Failed to connect to {self.account_type} account for position sizing")
            return 0.01

        account_info = mt5.account_info()
        if account_info is None:
            print("Failed to get account info.")
            return 0.01

        balance = account_info.balance
        risk_amount = balance * (config.RISK_PERCENT / 100)

        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Failed to get symbol info for {symbol}")
            return 0.01

        # Get pip value for proper position sizing
        point = symbol_info.point

        # For most forex pairs, 1 pip = 0.0001, for JPY pairs 1 pip = 0.01
        pip_size = 0.01 if "JPY" in symbol else 0.0001

        # Calculate pip value in account currency (USD)
        # For standard lot (100,000 units), 1 pip = $10 for most pairs
        # For mini lot (10,000 units), 1 pip = $1
        # For micro lot (1,000 units), 1 pip = $0.10

        # Simplified calculation: assume 1 standard lot = $10 per pip for major pairs
        pip_value_per_lot = 10.0  # USD per pip for 1 standard lot

        # Calculate position size based on risk
        # Risk amount / (stop loss in pips * pip value per lot)
        if sl_pips <= 0:
            print("Invalid stop loss pips for position sizing.")
            return 0.01

        volume = risk_amount / (sl_pips * pip_value_per_lot)

        # Convert to lots (round to 2 decimal places)
        volume = round(volume, 2)
        
        min_volume = symbol_info.volume_min
        max_volume = symbol_info.volume_max
        volume_step = symbol_info.volume_step

        # Clamp and step-align the volume
        volume = max(min_volume, volume)
        volume = min(max_volume, volume)
        volume = round(volume / volume_step) * volume_step
        
        return round(volume, 2)

    def execute_trade(self, symbol: str, decision: TradeDecision, latest_price: dict):
        """
        Executes a trade based on the AI's decision.
        """
        if decision.action == "HOLD":
            print("Decision is HOLD. No trade executed.")
            return None

        # Ensure we're connected to the correct account type
        if not mt5_manager.ensure_connection(self.account_type):
            print(f"Failed to connect to {self.account_type} account for trade execution")
            return None

        trade_type = mt5.ORDER_TYPE_BUY if decision.action == "BUY" else mt5.ORDER_TYPE_SELL
        tick_info = mt5.symbol_info_tick(symbol)
        if tick_info is None:
            print(f"Failed to get tick info for {symbol}")
            return None

        price = tick_info.ask if trade_type == mt5.ORDER_TYPE_BUY else tick_info.bid
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Failed to get symbol info for {symbol}")
            return None

        point = symbol_info.point

        # Calculate pip size (0.01 for JPY pairs, 0.0001 for others)
        pip_size = 0.01 if "JPY" in symbol else 0.0001

        # Calculate stop loss and take profit levels
        sl_distance = decision.sl_pips * pip_size
        tp_distance = decision.sl_pips * decision.tp_ratio * pip_size

        if trade_type == mt5.ORDER_TYPE_BUY:
            sl = price - sl_distance
            tp = price + tp_distance
        else:  # SELL
            sl = price + sl_distance
            tp = price - tp_distance

        volume = self.calculate_position_size(symbol, decision.sl_pips)
        
        if volume <= 0:
            print(f"Invalid volume {volume}, cannot place trade.")
            return None

        comment = f"AI Trade | Confidence: {decision.confidence:.2%}"

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": trade_type,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": self.slippage,
            "magic": 23400,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        for attempt in range(3): # Retry up to 3 times
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"Trade executed successfully: {result.order}")
                return result
            
            print(f"Order failed (attempt {attempt+1}): {result.comment}")
            time.sleep(1)
            
        print("Failed to execute trade after 3 attempts.")
        return None 