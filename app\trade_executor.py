import MetaTrader5 as mt5
import time
import numpy as np
from datetime import datetime, timedelta
from app.config import config
from app.ai_brain import TradeDecision
from app.mt5_manager import mt5_manager

class TradeExecutor:
    """
    Enhanced trade executor with advanced risk management and position management.
    """
    def __init__(self, account_type='demo'):
        self.account_type = account_type
        self.slippage = 5 # Max allowed deviation in points

        # Enhanced Risk Management
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        self.daily_loss_limit = 0.05  # 5% daily loss limit
        self.position_scale_factor = 1.0  # Dynamic position sizing
        self.last_reset_date = datetime.now().date()

        # Trailing Stop Management
        self.trailing_stops = {}  # {position_id: {'initial_sl': float, 'current_sl': float, 'trail_distance': float}}

        # Partial Profit Taking
        self.profit_targets = {}  # {position_id: {'target_1': float, 'target_2': float, 'taken_partial': bool}}

    def calculate_position_size(self, symbol: str, sl_pips: float) -> float:
        """
        Calculates position size based on risk percentage and stop loss.
        """
        # Ensure we're connected to the correct account
        if not mt5_manager.ensure_connection(self.account_type):
            print(f"Failed to connect to {self.account_type} account for position sizing")
            return 0.01

        account_info = mt5.account_info()
        if account_info is None:
            print("Failed to get account info.")
            return 0.01

        balance = account_info.balance
        risk_amount = balance * (config.RISK_PERCENT / 100)

        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Failed to get symbol info for {symbol}")
            return 0.01

        # Get pip value for proper position sizing
        point = symbol_info.point

        # For most forex pairs, 1 pip = 0.0001, for JPY pairs 1 pip = 0.01
        pip_size = 0.01 if "JPY" in symbol else 0.0001

        # Calculate pip value in account currency (USD)
        # For standard lot (100,000 units), 1 pip = $10 for most pairs
        # For mini lot (10,000 units), 1 pip = $1
        # For micro lot (1,000 units), 1 pip = $0.10

        # Simplified calculation: assume 1 standard lot = $10 per pip for major pairs
        pip_value_per_lot = 10.0  # USD per pip for 1 standard lot

        # Calculate position size based on risk
        # Risk amount / (stop loss in pips * pip value per lot)
        if sl_pips <= 0:
            print("Invalid stop loss pips for position sizing.")
            return 0.01

        volume = risk_amount / (sl_pips * pip_value_per_lot)

        # Convert to lots (round to 2 decimal places)
        volume = round(volume, 2)
        
        min_volume = symbol_info.volume_min
        max_volume = symbol_info.volume_max
        volume_step = symbol_info.volume_step

        # Clamp and step-align the volume
        volume = max(min_volume, volume)
        volume = min(max_volume, volume)
        volume = round(volume / volume_step) * volume_step
        
        return round(volume, 2)

    def check_daily_reset(self):
        """Reset daily counters if it's a new day"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.consecutive_losses = 0
            self.position_scale_factor = 1.0
            self.last_reset_date = current_date
            print("Daily risk management counters reset")

    def check_risk_conditions(self) -> bool:
        """Check if we should halt trading due to risk conditions"""
        self.check_daily_reset()

        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            print(f"🚨 RISK HALT: {self.consecutive_losses} consecutive losses reached. Reducing position size.")
            self.position_scale_factor = max(0.25, self.position_scale_factor * 0.5)  # Reduce to 25% minimum
            self.consecutive_losses = 0  # Reset after scaling down

        # Check daily loss limit
        account_info = mt5.account_info()
        if account_info:
            daily_start_balance = account_info.balance / (1 - self.daily_loss_limit)  # Approximate
            current_loss_pct = (daily_start_balance - account_info.equity) / daily_start_balance

            if current_loss_pct >= self.daily_loss_limit:
                print(f"🚨 DAILY LOSS LIMIT: {current_loss_pct:.1%} loss reached. Halting trading.")
                return False

        return True

    def update_trailing_stops(self):
        """Update trailing stops for all open positions"""
        if not mt5_manager.ensure_connection(self.account_type):
            return

        positions = mt5.positions_get()
        if not positions:
            return

        for position in positions:
            position_id = position.ticket
            symbol = position.symbol

            if position_id not in self.trailing_stops:
                continue

            current_price = mt5.symbol_info_tick(symbol)
            if not current_price:
                continue

            trail_data = self.trailing_stops[position_id]
            current_sl = trail_data['current_sl']
            trail_distance = trail_data['trail_distance']

            # Calculate new trailing stop
            if position.type == mt5.ORDER_TYPE_BUY:
                new_sl = current_price.bid - trail_distance
                if new_sl > current_sl:
                    self.modify_position_sl(position_id, new_sl)
                    trail_data['current_sl'] = new_sl
                    print(f"📈 Trailing stop updated for BUY {symbol}: {new_sl:.5f}")
            else:  # SELL
                new_sl = current_price.ask + trail_distance
                if new_sl < current_sl:
                    self.modify_position_sl(position_id, new_sl)
                    trail_data['current_sl'] = new_sl
                    print(f"📉 Trailing stop updated for SELL {symbol}: {new_sl:.5f}")

    def modify_position_sl(self, position_id: int, new_sl: float):
        """Modify stop loss for a position"""
        request = {
            "action": mt5.TRADE_ACTION_SLTP,
            "position": position_id,
            "sl": new_sl,
        }

        result = mt5.order_send(request)
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            print(f"Failed to modify SL for position {position_id}: {result.comment}")

    def check_partial_profits(self):
        """Check and execute partial profit taking"""
        if not mt5_manager.ensure_connection(self.account_type):
            return

        positions = mt5.positions_get()
        if not positions:
            return

        for position in positions:
            position_id = position.ticket

            if position_id not in self.profit_targets:
                continue

            profit_data = self.profit_targets[position_id]

            # Check if we should take partial profits (50% at 1:1 risk/reward)
            if not profit_data['taken_partial'] and position.profit > 0:
                entry_price = position.price_open
                current_profit_pips = abs(position.profit) / (position.volume * 10)  # Approximate

                if current_profit_pips >= profit_data['target_1']:
                    self.take_partial_profit(position, 0.5)  # Close 50% of position
                    profit_data['taken_partial'] = True
                    print(f"💰 Partial profit taken for {position.symbol}: 50% closed")

    def take_partial_profit(self, position, percentage: float):
        """Close partial position for profit taking"""
        close_volume = round(position.volume * percentage, 2)

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "position": position.ticket,
            "symbol": position.symbol,
            "volume": close_volume,
            "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
            "deviation": self.slippage,
            "comment": f"Partial profit {percentage*100:.0f}%",
        }

        result = mt5.order_send(request)
        if result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"✅ Partial profit executed: {close_volume} lots closed")
        else:
            print(f"❌ Partial profit failed: {result.comment}")

    def execute_trade(self, symbol: str, decision: TradeDecision, latest_price: dict):
        """
        Executes a trade based on the AI's decision with enhanced risk management.
        """
        if decision.action == "HOLD":
            print("Decision is HOLD. No trade executed.")
            return None

        # Check risk conditions before trading
        if not self.check_risk_conditions():
            print("🚨 Risk conditions failed. Trade execution halted.")
            return None

        # Update trailing stops and check partial profits for existing positions
        self.update_trailing_stops()
        self.check_partial_profits()

        # Ensure we're connected to the correct account type
        if not mt5_manager.ensure_connection(self.account_type):
            print(f"Failed to connect to {self.account_type} account for trade execution")
            return None

        trade_type = mt5.ORDER_TYPE_BUY if decision.action == "BUY" else mt5.ORDER_TYPE_SELL
        tick_info = mt5.symbol_info_tick(symbol)
        if tick_info is None:
            print(f"Failed to get tick info for {symbol}")
            return None

        price = tick_info.ask if trade_type == mt5.ORDER_TYPE_BUY else tick_info.bid
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Failed to get symbol info for {symbol}")
            return None

        point = symbol_info.point

        # Calculate pip size (0.01 for JPY pairs, 0.0001 for others)
        pip_size = 0.01 if "JPY" in symbol else 0.0001

        # Calculate stop loss and take profit levels
        sl_distance = decision.sl_pips * pip_size
        tp_distance = decision.sl_pips * decision.tp_ratio * pip_size

        if trade_type == mt5.ORDER_TYPE_BUY:
            sl = price - sl_distance
            tp = price + tp_distance
        else:  # SELL
            sl = price + sl_distance
            tp = price - tp_distance

        volume = self.calculate_position_size(symbol, decision.sl_pips)

        # Apply dynamic position scaling based on recent performance
        volume = volume * self.position_scale_factor
        volume = round(volume, 2)

        if volume <= 0:
            print(f"Invalid volume {volume}, cannot place trade.")
            return None

        comment = f"AI Trade | Confidence: {decision.confidence:.2%}"

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": trade_type,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": self.slippage,
            "magic": 23400,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        for attempt in range(3): # Retry up to 3 times
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"✅ Trade executed successfully: {result.order}")

                # Set up trailing stop and profit targets
                if hasattr(result, 'order') and result.order:
                    position_id = result.order

                    # Setup trailing stop (trail at 50% of original SL distance)
                    trail_distance = sl_distance * 0.5
                    self.trailing_stops[position_id] = {
                        'initial_sl': sl,
                        'current_sl': sl,
                        'trail_distance': trail_distance
                    }

                    # Setup profit targets
                    target_1_pips = decision.sl_pips * 1.0  # 1:1 risk/reward for partial profit
                    target_2_pips = decision.sl_pips * decision.tp_ratio  # Full target

                    self.profit_targets[position_id] = {
                        'target_1': target_1_pips,
                        'target_2': target_2_pips,
                        'taken_partial': False
                    }

                    print(f"📊 Risk management setup: Trailing stop at {trail_distance:.5f}, Partial profit at {target_1_pips} pips")

                # Reset consecutive losses on successful trade
                if hasattr(self, 'consecutive_losses'):
                    self.consecutive_losses = 0

                return result

            print(f"❌ Order failed (attempt {attempt+1}): {result.comment}")
            time.sleep(1)

        print("❌ Failed to execute trade after 3 attempts.")
        # Increment consecutive losses on failed execution
        self.consecutive_losses += 1
        return None

    def on_trade_closed(self, position_id: int, profit: float):
        """Called when a trade is closed to update risk management stats"""
        # Clean up tracking data
        if position_id in self.trailing_stops:
            del self.trailing_stops[position_id]
        if position_id in self.profit_targets:
            del self.profit_targets[position_id]

        # Update consecutive loss counter
        if profit < 0:
            self.consecutive_losses += 1
            print(f"📉 Trade closed with loss. Consecutive losses: {self.consecutive_losses}")
        else:
            self.consecutive_losses = 0
            print(f"📈 Trade closed with profit: ${profit:.2f}")

        # Gradually restore position size after successful trades
        if profit > 0 and self.position_scale_factor < 1.0:
            self.position_scale_factor = min(1.0, self.position_scale_factor * 1.1)
            print(f"📊 Position scale factor increased to {self.position_scale_factor:.2f}")

    def get_risk_status(self) -> dict:
        """Get current risk management status"""
        return {
            "consecutive_losses": self.consecutive_losses,
            "position_scale_factor": self.position_scale_factor,
            "max_consecutive_losses": self.max_consecutive_losses,
            "daily_loss_limit": self.daily_loss_limit,
            "active_trailing_stops": len(self.trailing_stops),
            "active_profit_targets": len(self.profit_targets)
        }