import { useState, useEffect } from 'react';
import api from '../services/api';
import { Play, Calendar, TrendingUp } from 'lucide-react';

const BacktestPanel: React.FC = () => {
  const [results, setResults] = useState<any[]>([]);
  const [currentBacktest, setCurrentBacktest] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [botStatus, setBotStatus] = useState<string>('idle');
  const [runningBacktest, setRunningBacktest] = useState(false);
  const [backtestForm, setBacktestForm] = useState({
    symbol: 'EURUSD',
    start_date: '2024-01-01',
    end_date: '2024-06-01',
    initial_balance: 10000
  });

  // Fetch backtest results and current status
  const fetchBacktestData = async () => {
    setLoading(true);
    setError(null);
    try {
      // In a real implementation, these would be separate API calls
      const resultsData = await api.getBacktestResults();
      setResults(resultsData.completed_backtests || []);
      
      if (resultsData.current_backtest) {
        setCurrentBacktest(resultsData.current_backtest);
        setBotStatus('backtesting');
      } else {
        setCurrentBacktest(null);
        setBotStatus(resultsData.status || 'idle');
      }
    } catch (e: any) {
      setError(e.message);
      setBotStatus('error');
    } finally {
      setLoading(false);
    }
  };

  // Run a new backtest
  const runBacktest = async () => {
    setRunningBacktest(true);
    setError(null);
    try {
      const result = await api.backtest(backtestForm);
      console.log('Backtest completed:', result);
      // Refresh the results after completion
      await fetchBacktestData();
    } catch (e: any) {
      setError(`Backtest failed: ${e.message}`);
    } finally {
      setRunningBacktest(false);
    }
  };

  // Fetch data on component mount and every 30 seconds
  useEffect(() => {
    fetchBacktestData();
    
    // Poll for updates
    const interval = setInterval(() => {
      fetchBacktestData();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Render status badge based on bot status
  const renderStatusBadge = () => {
    switch (botStatus) {
      case 'backtesting':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-blue rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-neon-blue font-mono">Backtesting in Progress</span>
          </div>
        );
      case 'analyzing':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-purple rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-neon-purple font-mono">Analyzing Results</span>
          </div>
        );
      case 'idle':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-green rounded-full"></div>
            <span className="text-sm font-medium text-neon-green font-mono">Ready</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-red rounded-full"></div>
            <span className="text-sm font-medium text-neon-red font-mono">Error</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2 bg-terminal-card rounded-full px-4 py-2 border border-terminal-border">
            <div className="w-2.5 h-2.5 bg-neon-yellow rounded-full"></div>
            <span className="text-sm font-medium text-neon-yellow font-mono">{botStatus}</span>
          </div>
        );
    }
  };

  return (
    <div className="terminal-card p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-terminal-text">AI Backtest Results</h2>
        {renderStatusBadge()}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-bold text-terminal-text">Bot Activity</h3>
          <button onClick={fetchBacktestData} className="px-4 py-2 rounded-lg bg-terminal-surface text-terminal-muted border border-terminal-border font-mono hover:bg-terminal-card/50 transition-all duration-300" disabled={loading}>
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
        <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
          {loading ? (
            <div className="text-neon-blue font-mono">Loading bot activity data...</div>
          ) : error ? (
            <div className="text-neon-red font-mono">{error}</div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Last Backtest Run:</span>
                <span className="text-terminal-text">{results.length > 0 ? new Date(results[0].timestamp).toLocaleString() : 'Never'}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Backtests Completed:</span>
                <span className="text-terminal-text">{results.length}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Next Scheduled Run:</span>
                <span className="text-terminal-text">Automated (AI Decides)</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">AI Decision Status:</span>
                <span className="text-neon-purple">Learning & Optimizing</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {currentBacktest && (
        <div className="mb-6">
          <h3 className="text-lg font-bold text-terminal-text mb-2">Current Backtest</h3>
          <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Symbol:</span>
                <span className="text-neon-blue">{currentBacktest.symbol}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Strategy:</span>
                <span className="text-neon-green">{currentBacktest.strategy}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Progress:</span>
                <div className="w-48 bg-terminal-bg rounded-full h-2">
                  <div className="bg-neon-blue h-2 rounded-full" style={{ width: `${currentBacktest.progress}%` }}></div>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Started:</span>
                <span className="text-terminal-text">{new Date(currentBacktest.start_time).toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between text-sm font-mono">
                <span className="text-terminal-muted">Estimated Completion:</span>
                <span className="text-terminal-text">{new Date(currentBacktest.estimated_completion).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {results.length > 0 && (
        <div>
          <h3 className="text-lg font-bold text-terminal-text mb-2">Recent Backtest Results</h3>
          <div className="space-y-4">
            {results.map((result, index) => (
              <div key={index} className="terminal-surface p-4 rounded-lg border border-terminal-border">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <span className="text-neon-blue font-bold font-mono">{result.symbol}</span>
                    <span className="text-terminal-muted font-mono ml-2">({result.strategy})</span>
                  </div>
                  <span className="text-xs text-terminal-muted font-mono">{new Date(result.timestamp).toLocaleString()}</span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-xs text-terminal-muted font-mono mb-1">Total Trades</div>
                    <div className="text-lg font-bold text-neon-blue font-mono">{result.performance.total_trades}</div>
                  </div>
                  <div>
                    <div className="text-xs text-terminal-muted font-mono mb-1">Win Rate</div>
                    <div className="text-lg font-bold text-neon-green font-mono">{(result.performance.win_rate * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-xs text-terminal-muted font-mono mb-1">Profit Factor</div>
                    <div className="text-lg font-bold text-neon-yellow font-mono">{result.performance.profit_factor.toFixed(2)}</div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs font-mono">
                  <span className="text-terminal-muted">AI Assessment:</span>
                  <span className={result.ai_assessment.includes('Optimal') ? 'text-neon-green' : 'text-neon-yellow'}>
                    {result.ai_assessment || 'Pending Analysis'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Manual Backtest Trigger */}
      <div className="mb-6">
        <h3 className="text-lg font-bold text-terminal-text mb-2">Run New Backtest</h3>
        <div className="terminal-surface p-4 rounded-lg border border-terminal-border">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-xs text-terminal-muted font-mono mb-1">Symbol</label>
              <select
                value={backtestForm.symbol}
                onChange={(e) => setBacktestForm({...backtestForm, symbol: e.target.value})}
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text font-mono text-sm focus:border-neon-blue focus:outline-none"
              >
                <option value="EURUSD">EUR/USD</option>
                <option value="GBPUSD">GBP/USD</option>
                <option value="USDJPY">USD/JPY</option>
                <option value="USDCHF">USD/CHF</option>
                <option value="AUDUSD">AUD/USD</option>
                <option value="USDCAD">USD/CAD</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-terminal-muted font-mono mb-1">Start Date</label>
              <input
                type="date"
                value={backtestForm.start_date}
                onChange={(e) => setBacktestForm({...backtestForm, start_date: e.target.value})}
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text font-mono text-sm focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label className="block text-xs text-terminal-muted font-mono mb-1">End Date</label>
              <input
                type="date"
                value={backtestForm.end_date}
                onChange={(e) => setBacktestForm({...backtestForm, end_date: e.target.value})}
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text font-mono text-sm focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label className="block text-xs text-terminal-muted font-mono mb-1">Initial Balance</label>
              <input
                type="number"
                value={backtestForm.initial_balance}
                onChange={(e) => setBacktestForm({...backtestForm, initial_balance: Number(e.target.value)})}
                className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text font-mono text-sm focus:border-neon-blue focus:outline-none"
                min="1000"
                step="1000"
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-terminal-muted font-mono">
              Run a historical backtest to evaluate strategy performance
            </div>
            <button
              onClick={runBacktest}
              disabled={runningBacktest || loading}
              className="flex items-center space-x-2 px-4 py-2 bg-neon-blue/20 border border-neon-blue/50 rounded-lg text-neon-blue font-mono hover:bg-neon-blue/30 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play size={16} />
              <span>{runningBacktest ? 'Running...' : 'Run Backtest'}</span>
            </button>
          </div>
        </div>
      </div>

      {results.length === 0 && !currentBacktest && !loading && !error && (
        <div className="bg-terminal-bg border border-terminal-border rounded-lg p-6 text-center">
          <div className="text-terminal-muted font-mono mb-2">No backtest data available</div>
          <div className="text-xs text-terminal-muted font-mono">Use the form above to run your first backtest</div>
        </div>
      )}
    </div>
  );
};

export default BacktestPanel; 