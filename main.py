import schedule
import time
import pandas as pd
from datetime import datetime
import os
import MetaTrader5 as mt5

from app.config import config
from app.data_feeder import DataFeeder
from app.ai_brain import MLBrain, HybridBrain, TradeDecision
from app.trade_executor import TradeExecutor
from app.news_filter import NewsFilter
from app.database import db
from app.anomaly_detector import AnomalyDetector
from app.auto_tuner import AutoTuner
from app.explainability import ExplainabilityEngine

# --- Safety Globals ---
# In a more advanced system, this state would be managed in a database or a dedicated state manager class.
daily_drawdown_limit = config.RISK_PERCENT 
daily_equity_start = None
trading_halted = False

def check_safety_protocols(symbol: str, df: pd.DataFrame, news_filter: NewsFilter) -> bool:
    """
    Runs all safety checks. Returns True if safe to trade, False otherwise.
    """
    global trading_halted, daily_equity_start

    # 1. Check daily drawdown
    if daily_equity_start is None:
        daily_equity_start = mt5.account_info().equity

    current_equity = mt5.account_info().equity
    drawdown_pct = (daily_equity_start - current_equity) / daily_equity_start * 100

    if drawdown_pct >= daily_drawdown_limit:
        if not trading_halted:
            print(f"CRITICAL: Daily drawdown limit of {daily_drawdown_limit}% reached. Halting all trades for the day.")
            trading_halted = True
        return False
    
    # 2. Check for high-impact news
    if news_filter.is_news_imminent(symbol):
        return False

    # 3. Circuit breaker for volatility spikes
    # Using ATR as a percentage of price as a volatility metric.
    atr_threshold = 0.5 # e.g., halt if ATR is > 0.5% of the price
    latest_atr = df['atr'].iloc[-1]
    latest_close = df['close'].iloc[-1]
    volatility_metric = (latest_atr / latest_close) * 100
    
    if volatility_metric > atr_threshold:
        print(f"VOLATILITY HALT: ATR ({volatility_metric:.2f}%) exceeds threshold of {atr_threshold}%. Skipping trade.")
        return False

    return True

def reset_daily_protocols():
    """Resets daily limits. To be scheduled to run once a day."""
    global daily_equity_start, trading_halted
    daily_equity_start = None
    trading_halted = False
    print("Daily safety protocols have been reset.")

def trade_cycle(feeder, hybrid_brain, executor, news_filter, anomaly_detector, explain_engine, ml_brain):
    """
    Executes a single trading cycle: fetch data, get decision, execute.
    """
    print(f"--- Running trade cycle at {datetime.now()} ---")
    if trading_halted:
        print("Trading is currently halted due to safety protocol breach.")
        return

    try:
        for symbol in config.SYMBOLS:
            print(f"Analyzing {symbol}...")
            
            # 1. Get new data
            latest_data = feeder.get_features(symbol)
            if latest_data.empty:
                print(f"No data for {symbol}, skipping.")
                continue

            # 2. Run safety checks
            if not check_safety_protocols(symbol, latest_data, news_filter):
                continue

            # 3. Generate AI decision
            if ml_brain.model is None:
                print("ML model not trained. Cannot generate prediction. Skipping cycle.")
                return 

            decision = hybrid_brain.get_decision(symbol, latest_data)
            
            print(f"AI Decision for {symbol}: {decision.action} (Confidence: {decision.confidence:.2%})")

            # 4. If not HOLD: Execute trade
            if decision.action != "HOLD":
                result = executor.execute_trade(symbol, decision, latest_data.iloc[-1])
                trade_id = db.log_trade(decision, symbol, result)
                
                # Log indicator values for this trade
                db.log_indicators(trade_id, latest_data.iloc[-1].to_dict())
                
                # EXPLAINABILITY: Explain 10% of trades
                if explain_engine.should_explain():
                    features = ml_brain.features_from_df(latest_data).iloc[[-1]]
                    explain_engine.explain_trade(
                        trade_id,
                        features.iloc[-1].to_dict(),
                        features.values,
                        decision.confidence,
                        decision.action,
                        symbol
                    )

    except Exception as e:
        print(f"An error occurred during the trade cycle: {e}")

def anomaly_detection_cycle(anomaly_detector):
    if anomaly_detector.should_reduce_position():
        print("Anomaly detected! Reducing position size by 50% for next cycle.")
        config.RISK_PERCENT = max(1.0, config.RISK_PERCENT * 0.5)
    else:
        print(f"Anomaly score OK: {anomaly_detector.get_last_score():.2f}")

def auto_tuning_cycle(auto_tuner):
    auto_tuner.apply_tuning()

def main():
    """Main function to initialize and run the bot."""
    print("Forex Trading Bot starting...")
    
    # --- Initialization ---
    print("Initializing MT5 connection...")
    if not mt5.initialize(
        login=config.MT5_LOGIN,
        password=config.MT5_PASSWORD,
        server=config.MT5_SERVER,
        path=config.MT5_PATH
    ):
        print("MT5 initialization failed. Please check credentials in .env and MT5 path.")
        mt5.shutdown()
        return  # Exit if connection fails

    print("MT5 Connected Successfully!")

    # Initialize components once
    feeder = DataFeeder()
    ml_brain = MLBrain()
    hybrid_brain = HybridBrain(ml_brain)
    executor = TradeExecutor()
    news_filter = NewsFilter()
    anomaly_detector = AnomalyDetector()
    explain_engine = ExplainabilityEngine(ml_brain)
    auto_tuner = AutoTuner()

    # --- Scheduling ---
    # Pass initialized components to the trade cycle
    schedule.every(15).minutes.do(
        trade_cycle, feeder, hybrid_brain, executor, news_filter, anomaly_detector, explain_engine, ml_brain
    )
    schedule.every().day.at("00:01").do(reset_daily_protocols)
    schedule.every(4).hours.do(anomaly_detection_cycle, anomaly_detector)
    schedule.every().sunday.at("04:00").do(auto_tuning_cycle, auto_tuner)

    print("Scheduler started. Running initial trade cycle...")
    # Run once immediately at the start
    trade_cycle(feeder, hybrid_brain, executor, news_filter, anomaly_detector, explain_engine, ml_brain)
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("Bot shutting down...")
    finally:
        mt5.shutdown()
        print("MT5 connection shut down.")

if __name__ == "__main__":
    main() 