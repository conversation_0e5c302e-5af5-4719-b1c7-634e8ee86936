import React, { useState, useEffect } from 'react';
import { Play, Pause, Square, TrendingUp, TrendingDown, AlertTriangle, Power, Settings, Zap, RefreshCw } from 'lucide-react';
import api from '../services/api';

interface BotStatus {
  isRunning: boolean;
  isPaused: boolean;
  lastAction: string;
  uptime: string;
  tradesCount: number;
  emergencyStop: boolean;
  lastUpdate: string;
}

interface TradeStats {
  totalTrades: number;
  winRate: number;
  profit: number;
  lastTradeTime: string;
}

const BotControlPanel: React.FC = () => {
  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    isPaused: false,
    lastAction: 'Initializing...',
    uptime: '0h 0m',
    tradesCount: 0,
    emergencyStop: false,
    lastUpdate: new Date().toLocaleTimeString()
  });

  const [tradeStats, setTradeStats] = useState<TradeStats>({
    totalTrades: 0,
    winRate: 0,
    profit: 0,
    lastTradeTime: 'Never'
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState<string | null>(null);
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');

  // Fetch real bot status from backend
  const fetchBotStatus = async () => {
    try {
      setError(null);
      const status = await api.getBotStatus();
      const isRunning = status.status === 'running';

      // Also fetch trade stats and account info
      const [accountStats, demoStats] = await Promise.all([
        api.getAccountStats().catch(() => null),
        api.getDemoAccountStats().catch(() => null)
      ]);

      // Calculate uptime (simplified - you might want to track this in backend)
      const now = new Date();
      const uptimeHours = Math.floor(Math.random() * 24); // Placeholder - implement real uptime tracking
      const uptimeMinutes = Math.floor(Math.random() * 60);

      setBotStatus(prev => ({
        ...prev,
        isRunning,
        isPaused: !isRunning && !prev.emergencyStop,
        uptime: `${uptimeHours}h ${uptimeMinutes}m`,
        lastUpdate: now.toLocaleTimeString()
      }));

      // Update trade stats from account data
      if (accountStats || demoStats) {
        const stats = accountStats || demoStats;
        setTradeStats({
          totalTrades: stats.total_trades || 0,
          winRate: stats.win_rate || 0,
          profit: stats.profit || 0,
          lastTradeTime: stats.last_trade_time || 'Never'
        });
      }
    } catch (e: any) {
      setError(`Failed to fetch bot status: ${e.message}`);
    }
  };

  // Handle bot control actions
  const handleBotAction = async (action: string) => {
    setLoading(true);
    setError(null);

    try {
      switch (action) {
        case 'start':
        case 'resume':
          const startResult = await api.startBot();
          setBotStatus(prev => ({
            ...prev,
            isRunning: true,
            isPaused: false,
            lastAction: startResult.message,
            lastUpdate: new Date().toLocaleTimeString()
          }));
          break;

        case 'pause':
        case 'stop':
          const stopResult = await api.stopBot();
          setBotStatus(prev => ({
            ...prev,
            isRunning: false,
            isPaused: true,
            lastAction: stopResult.message,
            lastUpdate: new Date().toLocaleTimeString()
          }));
          break;

        case 'emergency':
          await api.stopBot();
          setBotStatus(prev => ({
            ...prev,
            emergencyStop: true,
            isRunning: false,
            isPaused: false,
            lastAction: 'EMERGENCY STOP ACTIVATED',
            lastUpdate: new Date().toLocaleTimeString()
          }));
          break;

        case 'reset':
          await api.startBot();
          setBotStatus(prev => ({
            ...prev,
            emergencyStop: false,
            isRunning: true,
            isPaused: false,
            lastAction: 'System reset and restarted',
            lastUpdate: new Date().toLocaleTimeString()
          }));
          break;
      }
    } catch (e: any) {
      setError(`Failed to ${action} bot: ${e.message}`);
    } finally {
      setLoading(false);
      setShowConfirmation(null);
    }
  };

  // Handle force trade execution
  const handleForceAction = async (action: 'buy' | 'sell') => {
    setLoading(true);
    setError(null);

    try {
      const result = await api.forceExecuteTrade({
        action,
        symbol: selectedSymbol,
        volume: 0.01
      });

      if (result.success) {
        setBotStatus(prev => ({
          ...prev,
          lastAction: `FORCE ${action.toUpperCase()} ${selectedSymbol} - Order #${result.order_id}`,
          tradesCount: prev.tradesCount + 1,
          lastUpdate: new Date().toLocaleTimeString()
        }));

        // Refresh trade stats
        await fetchBotStatus();
      } else {
        setError(`Force trade failed: ${result.message}`);
      }
    } catch (e: any) {
      setError(`Failed to execute force ${action}: ${e.message}`);
    } finally {
      setLoading(false);
      setShowConfirmation(null);
    }
  };

  // Initialize and set up polling
  useEffect(() => {
    fetchBotStatus();

    // Poll for updates every 10 seconds
    const interval = setInterval(fetchBotStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (botStatus.emergencyStop) return 'text-neon-red';
    if (botStatus.isPaused) return 'text-neon-yellow';
    if (botStatus.isRunning) return 'text-neon-green';
    return 'text-terminal-muted';
  };

  const getStatusText = () => {
    if (botStatus.emergencyStop) return 'Emergency Stop';
    if (botStatus.isPaused) return 'Paused';
    if (botStatus.isRunning) return 'Active';
    return 'Stopped';
  };

  const controlButtons = [
    {
      id: 'toggle',
      label: botStatus.isRunning ? 'Stop Bot' : 'Start Bot',
      icon: botStatus.isRunning ? Pause : Play,
      action: botStatus.isRunning ? 'stop' : 'start',
      color: botStatus.isRunning ? 'from-neon-yellow to-neon-orange' : 'from-neon-green to-neon-blue',
      disabled: botStatus.emergencyStop || loading
    },
    {
      id: 'emergency',
      label: botStatus.emergencyStop ? 'Reset System' : 'Emergency Stop',
      icon: botStatus.emergencyStop ? Power : Square,
      action: botStatus.emergencyStop ? 'reset' : 'emergency',
      color: botStatus.emergencyStop ? 'from-neon-blue to-neon-green' : 'from-neon-red to-neon-orange',
      disabled: loading,
      requiresConfirmation: true
    },
    {
      id: 'refresh',
      label: 'Refresh Status',
      icon: RefreshCw,
      action: 'refresh',
      color: 'from-neon-purple to-neon-blue',
      disabled: loading
    }
  ];

  const forceTradeButtons = [
    {
      id: 'forceBuy',
      label: 'Force Buy',
      icon: TrendingUp,
      action: 'buy',
      color: 'from-neon-green to-neon-blue',
      disabled: botStatus.emergencyStop || loading
    },
    {
      id: 'forceSell',
      label: 'Force Sell',
      icon: TrendingDown,
      action: 'sell',
      color: 'from-neon-red to-neon-orange',
      disabled: botStatus.emergencyStop || loading
    }
  ];

  const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'];

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-neon-green/20 rounded-lg">
            <Settings className="text-neon-green" size={24} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-terminal-text">Bot Control Panel</h2>
            <p className="text-sm text-terminal-muted font-mono">Real-time Trading Bot Management</p>
          </div>
        </div>
        <div className="text-xs text-terminal-muted font-mono">
          Last Update: {botStatus.lastUpdate}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-neon-red/10 border border-neon-red/30 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2 text-neon-red">
            <AlertTriangle size={16} />
            <span className="font-mono font-medium">{error}</span>
          </div>
        </div>
      )}

      {/* Bot Status Display */}
      <div className="terminal-surface p-5 rounded-lg mb-6 border-l-4" style={{ borderLeftColor: getStatusColor().replace('text-', 'var(--color-') }}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              botStatus.emergencyStop ? 'bg-neon-red/20' :
              botStatus.isPaused ? 'bg-neon-yellow/20' :
              botStatus.isRunning ? 'bg-neon-green/20' : 'bg-terminal-muted/20'
            }`}>
              {botStatus.emergencyStop ? <AlertTriangle className="text-neon-red" size={20} /> :
               botStatus.isPaused ? <Pause className="text-neon-yellow" size={20} /> :
               botStatus.isRunning ? <Zap className="text-neon-green" size={20} /> :
               <Square className="text-terminal-muted" size={20} />}
            </div>
            <div>
              <div className={`text-lg font-bold font-mono ${getStatusColor()}`}>
                Bot Status: {getStatusText()}
              </div>
              <div className="text-sm text-terminal-muted font-mono">
                Uptime: {botStatus.uptime} • Total Trades: {tradeStats.totalTrades}
              </div>
              <div className="text-xs text-terminal-muted font-mono">
                Win Rate: {tradeStats.winRate.toFixed(1)}% • P&L: ${tradeStats.profit.toFixed(2)}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-terminal-muted font-mono mb-1">Last Action</div>
            <div className="text-terminal-text font-mono font-medium">{botStatus.lastAction}</div>
          </div>
        </div>

        {botStatus.emergencyStop && (
          <div className="bg-neon-red/10 border border-neon-red/30 rounded-lg p-3 mt-4">
            <div className="flex items-center space-x-2 text-neon-red">
              <AlertTriangle size={16} />
              <span className="font-mono font-medium">Emergency stop activated. All trading halted.</span>
            </div>
          </div>
        )}
      </div>

      {/* Main Control Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {controlButtons.map((button) => (
          <button
            key={button.id}
            onClick={() => {
              if (button.action === 'refresh') {
                fetchBotStatus();
              } else if (button.requiresConfirmation) {
                setShowConfirmation(button.action);
              } else {
                handleBotAction(button.action);
              }
            }}
            disabled={button.disabled}
            className={`flex items-center justify-center space-x-3 bg-gradient-to-r ${button.color} hover:opacity-90 text-white py-4 px-6 rounded-lg transition-all duration-300 font-mono font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <button.icon size={20} className={loading && button.action === 'refresh' ? 'animate-spin' : ''} />
            <span>{loading && button.action !== 'refresh' ? 'Processing...' : button.label}</span>
          </button>
        ))}
      </div>

      {/* Force Trade Buttons */}
      <div className="terminal-surface p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold text-terminal-text font-mono mb-4">Manual Trading</h3>

        {/* Symbol Selector */}
        <div className="mb-4">
          <label className="block text-sm text-terminal-muted font-mono mb-2">Select Symbol:</label>
          <select
            value={selectedSymbol}
            onChange={(e) => setSelectedSymbol(e.target.value)}
            className="w-full px-3 py-2 bg-terminal-bg border border-terminal-border rounded text-terminal-text font-mono text-sm focus:border-neon-blue focus:outline-none"
            disabled={loading}
          >
            {symbols.map(symbol => (
              <option key={symbol} value={symbol}>{symbol}</option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {forceTradeButtons.map((button) => (
            <button
              key={button.id}
              onClick={() => setShowConfirmation(button.action)}
              disabled={button.disabled}
              className={`flex items-center justify-center space-x-2 bg-gradient-to-r ${button.color} hover:opacity-90 text-white py-3 px-4 rounded-lg transition-all duration-300 font-mono font-medium disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              <button.icon size={16} />
              <span>{loading ? 'Processing...' : `${button.label} ${selectedSymbol}`}</span>
            </button>
          ))}
        </div>

        <div className="mt-3 text-xs text-terminal-muted font-mono text-center">
          ⚠️ Manual trades will be executed immediately with 0.01 lot size
        </div>
      </div>

      {/* Connection Status */}
      <div className="grid grid-cols-2 gap-4">
        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-mono text-terminal-muted">MT5 Connection</span>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-neon-green">Online</span>
            </div>
          </div>
        </div>
        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-mono text-terminal-muted">API Status</span>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-neon-green">Connected</span>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="terminal-card p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-neon-yellow" size={24} />
              <h3 className="text-xl font-bold text-terminal-text font-mono">Confirm Action</h3>
            </div>
            <p className="text-terminal-muted font-mono mb-6">
              {showConfirmation === 'emergency' && 'This will immediately stop all trading and close open positions.'}
              {showConfirmation === 'buy' && `Execute a manual BUY order for ${selectedSymbol} with 0.01 lot size?`}
              {showConfirmation === 'sell' && `Execute a manual SELL order for ${selectedSymbol} with 0.01 lot size?`}
              {showConfirmation === 'reset' && 'Reset the emergency stop and resume normal operations?'}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  if (showConfirmation === 'buy' || showConfirmation === 'sell') {
                    handleForceAction(showConfirmation as 'buy' | 'sell');
                  } else {
                    handleBotAction(showConfirmation);
                  }
                }}
                className="flex-1 bg-gradient-to-r from-neon-red to-neon-orange text-white py-3 px-4 rounded-lg font-mono font-medium hover:opacity-90 transition-all duration-300"
              >
                Confirm
              </button>
              <button
                onClick={() => setShowConfirmation(null)}
                className="flex-1 terminal-surface text-terminal-text py-3 px-4 rounded-lg font-mono font-medium hover:bg-terminal-card transition-all duration-300 border border-terminal-border"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BotControlPanel;