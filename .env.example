# NexGen Forex Trading Bot Configuration

# ===========================================
# MT5 DEMO ACCOUNT (for Forward Testing & Backtesting)
# ===========================================
MT5_DEMO_LOGIN=***********
MT5_DEMO_PASSWORD=MzE*X2Hm
MT5_DEMO_SERVER=MetaQuotes-Demo

# ===========================================
# MT5 LIVE ACCOUNT (for Real Trading & Account Stats)
# ===========================================
MT5_LIVE_LOGIN=your_live_login
MT5_LIVE_PASSWORD=your_live_password
MT5_LIVE_SERVER=your_live_server

# ===========================================
# MT5 LEGACY SUPPORT (fallback to demo if not specified)
# ===========================================
MT5_LOGIN=***********
MT5_PASSWORD=MzE*X2Hm
MT5_SERVER=MetaQuotes-Demo
MT5_PATH=C:/Program Files/MetaTrader 5/terminal64.exe

# ===========================================
# TRADING PARAMETERS
# ===========================================
RISK_PERCENT=2.0
TP_RATIO=1.5
SYMBOLS=EURUSD,GBPUSD

# ===========================================
# AI CONFIGURATION
# ===========================================
OPENAI_API_KEY=********************************************************************************************************************************************************************


# ===========================================
# NEWS & DATA
# ===========================================
FINNHUB_API_KEY=your_finnhub_key
NEWS_SOURCE=mt5
CALENDAR_FILE_PATH=C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=forex_memory
POSTGRES_USER=postgres
POSTGRES_PASSWORD=TAKE1day!!

# ===========================================
# VECTOR STORE
# ===========================================
VECTOR_STORE_TYPE=chromadb
VECTOR_STORE_PATH=vector_store

# ===========================================
# VOICE FEATURES
# ===========================================
ELEVENLABS_API_KEY=your_elevenlabs_key
VOICE_ENABLED=True

# ===========================================
# ACCOUNT SEPARATION EXPLANATION
# ===========================================
# Demo Account Usage:
# - Forward testing (ForwardTestPanel)
# - Backtesting (BacktestPanel)
# - Chart data fetching
# - Strategy development
# - Safe testing environment
#
# Live Account Usage:
# - Real trading execution
# - Account statistics (AccountStats)
# - Live balance/equity monitoring
# - Actual money management
#
# This separation ensures:
# 1. Safe testing without risking real money
# 2. Real account data for monitoring
# 3. Clear distinction between demo and live operations
