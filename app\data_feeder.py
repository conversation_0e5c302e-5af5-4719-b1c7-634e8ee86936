import MetaTrader5 as mt5
import pandas as pd
import pandas_ta as ta
from app.config import config
from app.mt5_manager import mt5_manager

class DataFeeder:
    def __init__(self, account_type: str = 'demo'):
        """
        Initialize DataFeeder with specified account type
        account_type: 'demo' for backtesting/forward testing, 'live' for real trading
        """
        self.account_type = account_type

    def fetch_data(self, symbol: str, timeframe=mt5.TIMEFRAME_M15, num_candles=300) -> pd.DataFrame:
        """Fetch historical data for a symbol using the appropriate account"""
        try:
            rates = mt5_manager.get_price_data(symbol, timeframe, num_candles, self.account_type)

            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            return df
        except Exception as e:
            raise ValueError(f"Failed to fetch data for {symbol}: {str(e)}")
    
    def _find_bbands_col(self, bbands_df, prefix):
        # Find the first column that starts with the given prefix
        for col in bbands_df.columns:
            if col.startswith(prefix):
                return col
        raise KeyError(f"No BBANDS column found with prefix {prefix}")
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # RSI
        df['rsi'] = ta.rsi(df['close'], length=config.RSI_PERIOD)
        
        # MACD
        macd = ta.macd(
            df['close'], 
            fast=config.MACD_FAST,
            slow=config.MACD_SLOW,
            signal=config.MACD_SIGNAL
        )
        df['macd'] = macd['MACD_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        df['macd_signal'] = macd['MACDs_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        df['macd_diff'] = macd['MACDh_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        
        # Bollinger Bands
        bbands = ta.bbands(
            df['close'],
            length=config.BOLLINGER_PERIOD,
            std=config.BOLLINGER_STD
        )
        df['bb_upper'] = bbands[self._find_bbands_col(bbands, 'BBU_')]
        df['bb_middle'] = bbands[self._find_bbands_col(bbands, 'BBM_')]
        df['bb_lower'] = bbands[self._find_bbands_col(bbands, 'BBL_')]
        df['bb_pct'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Stochastic Oscillator
        stoch = ta.stoch(
            df['high'], 
            df['low'], 
            df['close'],
            k=config.STOCHASTIC_PERIOD
        )
        df['stoch_k'] = stoch['STOCHk_' + str(config.STOCHASTIC_PERIOD) + '_3_3']
        df['stoch_d'] = stoch['STOCHd_' + str(config.STOCHASTIC_PERIOD) + '_3_3']
        
        # ATR
        df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=14)
        
        return df.dropna()
    
    def get_features(self, symbol: str) -> pd.DataFrame:
        """Get processed data with indicators for a symbol"""
        raw_data = self.fetch_data(symbol)
        return self.calculate_indicators(raw_data) 